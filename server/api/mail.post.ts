import nodemailer from "nodemailer";

// IP 限流配置
const RATE_LIMIT = {
  maxRequests: 3, // 每个 IP 最多 3 次请求
  windowMs: 24 * 60 * 60 * 1000, // 24 小时窗口期
};

// 内存存储 IP 请求记录
const ipRequestMap = new Map<string, { count: number; firstRequest: number }>();

// 清理过期记录的函数
function cleanupExpiredRecords() {
  const now = Date.now();
  for (const [ip, record] of ipRequestMap.entries()) {
    if (now - record.firstRequest > RATE_LIMIT.windowMs) {
      ipRequestMap.delete(ip);
    }
  }
}

// 检查 IP 是否超过限制
function checkRateLimit(ip: string): { allowed: boolean; remaining: number } {
  cleanupExpiredRecords();

  const now = Date.now();
  const record = ipRequestMap.get(ip);

  if (!record) {
    // 首次请求
    ipRequestMap.set(ip, { count: 1, firstRequest: now });
    return { allowed: true, remaining: RATE_LIMIT.maxRequests - 1 };
  }

  // 检查是否在窗口期内
  if (now - record.firstRequest > RATE_LIMIT.windowMs) {
    // 窗口期已过，重置计数
    ipRequestMap.set(ip, { count: 1, firstRequest: now });
    return { allowed: true, remaining: RATE_LIMIT.maxRequests - 1 };
  }

  // 在窗口期内，检查是否超过限制
  if (record.count >= RATE_LIMIT.maxRequests) {
    return { allowed: false, remaining: 0 };
  }

  // 增加计数
  record.count++;
  return { allowed: true, remaining: RATE_LIMIT.maxRequests - record.count };
}

// 邮件配置
const config = {
  host: "smtp.mxhichina.com",
  port: 465,
  secure: true,
  user: "<EMAIL>",
  pass: "Baoya@2019",
  from: "赢睿科技 <<EMAIL>>",
  recipients: "<EMAIL>, <EMAIL>",
  // recipients: "<EMAIL>",
};

// 创建邮件传输器
const mailer = nodemailer.createTransport({
  host: config.host,
  port: config.port,
  secure: config.secure,
  auth: {
    user: config.user,
    pass: config.pass,
  },
});

// 定义请求体接口
interface InquiryRequest {
  name: string;
  phone: string;
  email: string;
  company: string;
  department?: string;
  content: string;
}

export default defineEventHandler(async (event) => {
  try {
    // 获取客户端 IP 地址
    const clientIP =
      getHeader(event, "x-forwarded-for") ||
      getHeader(event, "x-real-ip") ||
      getHeader(event, "cf-connecting-ip") ||
      event.node.req.socket?.remoteAddress ||
      "unknown";

    // 检查 IP 限流
    const rateLimitResult = checkRateLimit(clientIP);
    if (!rateLimitResult.allowed) {
      console.log(`Rate limit exceeded for IP: ${clientIP}`);
      throw createError({
        statusCode: 429,
        message: "请求过于频繁，请24小时后再试",
        data: {
          error: "RATE_LIMIT_EXCEEDED",
          message: "您今日的咨询次数已达上限，请明天再试或直接联系我们",
          retryAfter: "24小时后",
        },
      });
    }

    console.log(
      `Request from IP: ${clientIP}, remaining: ${rateLimitResult.remaining}`
    );

    // 读取请求体
    const body = await readBody<InquiryRequest>(event);

    // 验证必填字段
    if (
      !body.name ||
      !body.phone ||
      !body.email ||
      !body.company ||
      !body.content
    ) {
      throw createError({
        statusCode: 400,
        message: "Missing required fields",
      });
    }

    // 构建邮件 HTML 内容
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9fafb;">
        <div style="background-color: white; border-radius: 8px; padding: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
          <h1 style="color: #1f2937; margin-bottom: 20px; border-bottom: 3px solid #3b82f6; padding-bottom: 10px;">
            🚀 新的联系我们请求
          </h1>

          <div style="margin-bottom: 20px;">
            <h2 style="color: #374151; font-size: 18px; margin-bottom: 15px;">客户信息</h2>
            <table style="width: 100%; border-collapse: collapse;">
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #e5e7eb; font-weight: bold; color: #374151; width: 120px;">企业名称：</td>
                <td style="padding: 8px 0; border-bottom: 1px solid #e5e7eb; color: #1f2937;">${body.company
      }</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #e5e7eb; font-weight: bold; color: #374151;">联系人：</td>
                <td style="padding: 8px 0; border-bottom: 1px solid #e5e7eb; color: #1f2937;">${body.name
      }</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #e5e7eb; font-weight: bold; color: #374151;">联系电话：</td>
                <td style="padding: 8px 0; border-bottom: 1px solid #e5e7eb; color: #1f2937;">${body.phone
      }</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #e5e7eb; font-weight: bold; color: #374151;">邮箱地址：</td>
                <td style="padding: 8px 0; border-bottom: 1px solid #e5e7eb; color: #1f2937;">${body.email
      }</td>
              </tr>
              ${body.department
        ? `
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #e5e7eb; font-weight: bold; color: #374151;">部门：</td>
                <td style="padding: 8px 0; border-bottom: 1px solid #e5e7eb; color: #1f2937;">${body.department}</td>
              </tr>
              `
        : ""
      }
            </table>
          </div>

          <div style="margin-bottom: 20px;">
            <h2 style="color: #374151; font-size: 18px; margin-bottom: 15px;">咨询内容</h2>
            <div style="background-color: #f3f4f6; padding: 15px; border-radius: 6px; border-left: 4px solid #3b82f6;">
              <p style="color: #1f2937; line-height: 1.6; margin: 0; white-space: pre-wrap;">${body.content
      }</p>
            </div>
          </div>

          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; text-align: center;">
            <p style="color: #6b7280; font-size: 14px; margin: 0;">
              此邮件由赢睿科技官网自动发送 | 提交时间：${new Date().toLocaleString(
        "zh-CN",
        { timeZone: "Asia/Shanghai" }
      )}
            </p>
          </div>
        </div>
      </div>
    `;

    console.log(`Send email to ${config.recipients}`);

    // 发送邮件
    const info = await mailer.sendMail({
      from: config.from,
      to: config.recipients,
      subject: "[赢睿科技官网] 新业务咨询",
      html,
    });

    console.log(
      `Email sent to ${config.recipients} with message id ${info.messageId}`
    );

    // 返回成功响应
    return {
      success: true,
      message: "邮件发送成功",
      messageId: info.messageId,
    };
  } catch (error) {
    console.error("邮件发送失败:", error);

    throw createError({
      statusCode: 500,
      message: "邮件发送失败，请稍后重试",
    });
  }
});
