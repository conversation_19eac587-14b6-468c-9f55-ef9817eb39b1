@echo off
chcp 65001 >nul
title 赢睿保险科技官网项目启动器

echo.
echo ========================================
echo    赢睿保险科技官网项目启动器
echo ========================================
echo.

:menu
echo 请选择要执行的操作：
echo.
echo [1] 启动开发服务器 (npm run dev)
echo [2] 构建生产版本 (npm run build)
echo [3] 预览生产版本 (npm run preview)
echo [4] 生成静态网站 (npm run generate)
echo [5] 安装依赖 (npm install)
echo [6] 清理缓存并重新安装
echo [0] 退出
echo.

set /p choice=请输入选项 (0-6): 

if "%choice%"=="1" goto dev
if "%choice%"=="2" goto build
if "%choice%"=="3" goto preview
if "%choice%"=="4" goto generate
if "%choice%"=="5" goto install
if "%choice%"=="6" goto clean
if "%choice%"=="0" goto exit
echo 无效选项，请重新选择
goto menu

:dev
echo.
echo 正在启动开发服务器...
echo 项目将在 http://localhost:3000 运行
echo 按 Ctrl+C 停止服务器
echo.
npm run dev
goto menu

:build
echo.
echo 正在构建生产版本...
npm run build
echo.
echo 构建完成！
pause
goto menu

:preview
echo.
echo 正在启动预览服务器...
echo 项目将在 http://localhost:3000 运行
echo 按 Ctrl+C 停止服务器
echo.
npm run preview
goto menu

:generate
echo.
echo 正在生成静态网站...
npm run generate
echo.
echo 静态网站生成完成！
pause
goto menu

:install
echo.
echo 正在安装项目依赖...
npm install
echo.
echo 依赖安装完成！
pause
goto menu

:clean
echo.
echo 正在清理缓存并重新安装依赖...
echo 删除 node_modules 文件夹...
if exist node_modules rmdir /s /q node_modules
echo 删除 package-lock.json...
if exist package-lock.json del package-lock.json
echo 清理 npm 缓存...
npm cache clean --force
echo 重新安装依赖...
npm install
echo.
echo 清理完成！
pause
goto menu

:exit
echo.
echo 感谢使用赢睿保险科技官网项目启动器！
echo.
pause
exit
