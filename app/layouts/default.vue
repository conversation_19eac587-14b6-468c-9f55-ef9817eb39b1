<template>
  <div class="min-h-screen">
    <!-- Header Section -->
    <header
      class="fixed top-0 left-0 right-0 z-50 bg-white shadow-xl/4"
      @mouseleave="handleHeaderMouseLeave"
    >
      <div class="container mx-auto px-6 py-6">
        <div class="flex items-center justify-between">
          <!-- Logo和导航菜单组合 - 靠左 -->
          <div class="flex items-center gap-12">
            <NuxtLink
              to="/"
              class="text-2xl font-bold text-primary-500 flex items-center gap-2"
              @click="closeSubnav"
            >
              <img src="/img/winray-logo.png" alt="Winray Logo" class="h-8" />
              <div class="mt-1.5">赢睿科技</div>
            </NuxtLink>

            <!-- 桌面端导航菜单 -->
            <nav class="hidden lg:block">
              <ul class="flex space-x-6">
                <li
                  v-for="menu in mainMenus"
                  :key="menu.id"
                  class="relative"
                  :class="{ 'main-menu-active': activeMenu === menu.id }"
                  @mouseenter="activeMenu = menu.id"
                >
                  <NuxtLink
                    :to="menu.link"
                    class="text-lg transition-all duration-200 flex items-center gap-1 px-3 py-2 rounded-lg"
                    :class="[
                      activeMenu === menu.id
                        ? 'text-primary-500'
                        : 'text-primary-700 hover:text-primary-500 hover:bg-primary-50',
                    ]"
                    @click="closeSubnav"
                  >
                    {{ menu.name }}
                    <UIcon
                      v-if="menu.children && menu.children.length > 0"
                      name="i-heroicons-chevron-down"
                      class="w-4 h-4 transition-transform duration-200"
                      :class="{ 'rotate-180': activeMenu === menu.id }"
                    />
                  </NuxtLink>
                </li>
              </ul>
            </nav>
          </div>

          <!-- 右侧按钮区域 -->
          <div class="flex items-center gap-4">
            <!-- 桌面端咨询按钮 -->
            <UButton
              variant="outline"
              size="lg"
              color="neutral"
              trailing
              class="hidden lg:flex group relative overflow-hidden bg-transparent border-2 border-primary-500 text-primary-600 hover:text-white hover:border-primary-600 px-8 py-3 text-base font-semibold rounded-xl transition-all duration-300 hover:shadow-lg hover:shadow-primary-500/25 hover:-translate-y-0.5"
              @click="openInquiryDrawer"
            >
              <template #trailing>
                <UIcon
                  name="i-heroicons-user-circle"
                  class="w-5 h-5 transition-transform duration-300 group-hover:scale-110"
                />
              </template>
              立即咨询

              <!-- 背景动画效果 -->
              <div
                class="absolute inset-0 bg-gradient-to-r from-primary-500 to-primary-600 translate-x-full group-hover:translate-x-0 transition-transform duration-300 ease-out -z-10"
              ></div>
            </UButton>

            <!-- 移动端汉堡菜单按钮 -->
            <button
              class="lg:hidden flex items-center justify-center w-10 h-10 rounded-lg bg-primary-50 text-primary-600 hover:bg-primary-100 transition-colors"
              @click="toggleMobileMenu"
              aria-label="打开菜单"
            >
              <UIcon
                :name="
                  isMobileMenuOpen ? 'i-heroicons-x-mark' : 'i-heroicons-bars-3'
                "
                class="w-6 h-6"
              />
            </button>
          </div>
        </div>
      </div>

      <!-- Subnav 子菜单区域 - 仅桌面端 -->
      <Transition name="subnav">
        <div
          v-if="activeMenu && getActiveMenuData()"
          class="hidden lg:block border-t border-primary-200"
        >
          <div class="container mx-auto px-6 py-8">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              <div
                v-for="(category, index) in getActiveMenuData()"
                :key="index"
                class="space-y-4"
              >
                <h3
                  class="text-lg font-semibold text-primary-900 border-b border-primary-200 pb-2"
                >
                  {{ category.title }}
                </h3>
                <ul class="space-y-2">
                  <li
                    v-for="item in category.items"
                    :key="item.id"
                    class="group"
                  >
                    <NuxtLink
                      :to="item.link"
                      class="text-primary-700 hover:text-primary-600 font-medium"
                      @click="closeSubnav"
                    >
                      {{ item.name }}
                    </NuxtLink>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </Transition>
    </header>

    <!-- 移动端菜单抽屉 -->
    <Transition name="mobile-menu">
      <div
        v-if="isMobileMenuOpen"
        class="fixed inset-0 z-50 lg:hidden"
        @click="closeMobileMenu"
      >
        <!-- 背景遮罩 -->
        <div
          class="absolute inset-0 bg-black/50 backdrop-blur-sm mobile-menu-overlay"
        ></div>

        <!-- 菜单内容 -->
        <div
          class="absolute top-0 right-0 w-80 h-full bg-white shadow-2xl overflow-y-auto mobile-menu-content"
          @click.stop
        >
          <!-- 移动端菜单头部 -->
          <div
            class="flex items-center justify-between p-6 border-b border-gray-200"
          >
            <h2 class="text-lg font-semibold text-primary-900">菜单</h2>
            <button
              @click="closeMobileMenu"
              class="p-2 rounded-lg hover:bg-gray-100 transition-colors"
              aria-label="关闭菜单"
            >
              <UIcon name="i-heroicons-x-mark" class="w-6 h-6 text-gray-600" />
            </button>
          </div>

          <!-- 移动端菜单内容 -->
          <nav class="p-6">
            <ul class="space-y-2">
              <li
                v-for="menu in mainMenus"
                :key="menu.id"
                class="mobile-menu-item"
              >
                <!-- 主菜单项 -->
                <div
                  class="flex items-center justify-between p-3 rounded-lg hover:bg-primary-50 active:bg-primary-100 transition-all duration-200 cursor-pointer touch-manipulation"
                  :class="{
                    'bg-primary-50 text-primary-600':
                      activeMobileMenu === menu.id,
                  }"
                  @click="toggleMobileSubmenu(menu.id)"
                  @touchstart=""
                >
                  <NuxtLink
                    :to="menu.link"
                    class="flex-1 text-base font-medium"
                    @click="closeMobileMenu"
                  >
                    {{ menu.name }}
                  </NuxtLink>
                  <UIcon
                    v-if="menu.children && menu.children.length > 0"
                    name="i-heroicons-chevron-down"
                    class="w-5 h-5 transition-transform duration-200"
                    :class="{ 'rotate-180': activeMobileMenu === menu.id }"
                  />
                </div>

                <!-- 移动端子菜单 -->
                <Transition name="mobile-submenu">
                  <div
                    v-if="activeMobileMenu === menu.id && menu.children"
                    class="ml-4 mt-2 space-y-1"
                  >
                    <div
                      v-for="(category, categoryIndex) in menu.children"
                      :key="categoryIndex"
                      class="space-y-2"
                    >
                      <h4
                        class="text-sm font-semibold text-primary-700 px-3 py-2"
                      >
                        {{ category.title }}
                      </h4>
                      <ul class="space-y-1">
                        <li v-for="item in category.items" :key="item.id">
                          <NuxtLink
                            :to="item.link"
                            class="block px-6 py-2 text-sm text-primary-600 hover:text-primary-500 hover:bg-primary-50 active:bg-primary-100 rounded-lg transition-all duration-200 touch-manipulation"
                            @click="closeMobileMenu"
                          >
                            {{ item.name }}
                          </NuxtLink>
                        </li>
                      </ul>
                    </div>
                  </div>
                </Transition>
              </li>
            </ul>

            <!-- 移动端咨询按钮 -->
            <div class="mt-8 pt-6 border-t border-gray-200">
              <UButton
                size="lg"
                class="w-full bg-primary-500 hover:bg-primary-600 text-white font-semibold py-3"
                @click="openInquiryDrawer"
              >
                <UIcon name="i-heroicons-user-circle" class="w-5 h-5 mr-2" />
                立即咨询
              </UButton>
            </div>
          </nav>
        </div>
      </div>
    </Transition>

    <!-- 主要内容区域 -->
    <main>
      <slot />
    </main>

    <!-- CTA 行动召唤 -->
    <section class="py-20 lg:py-28 bg-primary-900 relative overflow-hidden">
      <div class="container mx-auto px-6 relative">
        <div class="max-w-5xl mx-auto text-center text-white">
          <h2 class="text-4xl lg:text-6xl font-bold mb-8 leading-tight">
            开启 <span class="text-primary-500">AI</span> 驱动的保险科技新时代
          </h2>
          <p
            class="text-xl lg:text-2xl text-primary-300 mb-4 leading-relaxed max-w-4xl mx-auto font-light"
          >
            专业的 AI 保险科技团队，为您提供定制化的智能解决方案
          </p>
          <p
            class="text-lg text-primary-400 mb-12 max-w-3xl mx-auto font-light"
          >
            24 小时内专业回复 · 分钟级 AI 部署 · 全程技术支持
          </p>

          <div class="flex flex-col sm:flex-row gap-6 justify-center mb-12">
            <UButton
              size="xl"
              class="bg-primary-500 hover:bg-primary-600 text-white px-12 py-5 text-xl font-semibold"
              @click="openInquiryDrawer"
            >
              预约产品演示
            </UButton>
            <UButton
              variant="outline"
              size="xl"
              class="border-2 border-white text-white hover:bg-white hover:text-primary-900 px-12 py-5 text-xl font-semibold"
              @click="openInquiryDrawer"
            >
              售前电话咨询
            </UButton>
          </div>

          <!-- 联系信息 -->
          <div class="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div class="text-center">
              <div
                class="w-16 h-16 bg-primary-500/20 backdrop-blur rounded-xl flex items-center justify-center mx-auto mb-4"
              >
                <UIcon
                  name="i-heroicons-phone"
                  class="w-8 h-8 text-primary-400"
                />
              </div>
              <h3 class="text-lg font-semibold mb-2">专业顾问热线</h3>
              <p class="text-primary-400 text-xl font-bold">
                +86 181 2137 8388
              </p>
            </div>

            <div class="text-center">
              <div
                class="w-16 h-16 bg-primary-500/20 backdrop-blur rounded-xl flex items-center justify-center mx-auto mb-4"
              >
                <UIcon
                  name="i-heroicons-envelope"
                  class="w-8 h-8 text-primary-400"
                />
              </div>
              <h3 class="text-lg font-semibold mb-2">技术咨询邮箱</h3>
              <p class="text-primary-400 text-lg"><EMAIL></p>
            </div>

            <div class="text-center">
              <div
                class="w-16 h-16 bg-primary-500/20 backdrop-blur rounded-xl flex items-center justify-center mx-auto mb-4"
              >
                <UIcon
                  name="i-heroicons-clock"
                  class="w-8 h-8 text-primary-400"
                />
              </div>
              <h3 class="text-lg font-semibold mb-2">响应承诺</h3>
              <p class="text-primary-400 text-lg">24 小时内专业回复</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 页脚 -->
    <footer class="bg-primary-950 text-white py-16">
      <div class="container mx-auto px-6">
        <div
          class="grid grid-cols-1 md:grid-cols-2 lg:flex lg:flex-row gap-8 mb-12"
        >
          <!-- 公司介绍 -->
          <div class="md:col-span-2 lg:w-2/6">
            <img
              src="/img/winray-logo.png"
              alt="赢睿保险科技"
              class="h-8 mb-6 brightness-0 invert"
            />
            <p class="text-primary-300 text-sm leading-relaxed mb-6 font-light">
              赢睿保险科技是一家专注于 AI
              驱动的保险科技公司，总部位于上海，致力于通过人工智能、大数据、云计算技术为保险行业提供智能化解决方案，助力行业实现
              AI 驱动的数字化转型。
            </p>
            <div class="text-primary-400 text-sm space-y-2">
              <div class="flex items-center space-x-2">
                <UIcon
                  name="i-heroicons-phone"
                  class="w-4 h-4 text-primary-400"
                />
                <span>+86 181 2137 8388</span>
              </div>
              <div class="flex items-center space-x-2">
                <UIcon
                  name="i-heroicons-envelope"
                  class="w-4 h-4 text-primary-400"
                />
                <span><EMAIL></span>
              </div>
              <div class="flex items-center space-x-2">
                <UIcon
                  name="i-heroicons-map-pin"
                  class="w-4 h-4 text-primary-400"
                />
                <span>上海市杨浦区隆昌路 619 号 1 号楼 B102</span>
              </div>
            </div>
          </div>

          <!-- 产品体系 -->
          <div class="text-center lg:w-1/6">
            <h4 class="font-semibold mb-6 text-primary-200 text-lg">
              产品体系
            </h4>
            <ul class="space-y-3 text-sm text-primary-400">
              <li>
                <NuxtLink
                  to="/products/ai-risk-pricing"
                  class="hover:text-primary-300 transition-colors cursor-pointer"
                >
                  AI 风控定价系统
                </NuxtLink>
              </li>
              <li>
                <NuxtLink
                  to="/products/ai-claims"
                  class="hover:text-primary-300 transition-colors cursor-pointer"
                >
                  AI 理赔管理系统
                </NuxtLink>
              </li>
              <li>
                <NuxtLink
                  to="/products/ai-customer-service"
                  class="hover:text-primary-300 transition-colors cursor-pointer"
                >
                  AI 客服管理系统
                </NuxtLink>
              </li>
              <li>
                <NuxtLink
                  to="/products/ai-knowledge-base"
                  class="hover:text-primary-300 transition-colors cursor-pointer"
                >
                  AI 企业知识库
                </NuxtLink>
              </li>
              <li>
                <NuxtLink
                  to="/products/core-business-platform"
                  class="hover:text-primary-300 transition-colors cursor-pointer"
                >
                  保险综合业务中台
                </NuxtLink>
              </li>
              <li>
                <NuxtLink
                  to="/products/agency-management"
                  class="hover:text-primary-300 transition-colors cursor-pointer"
                >
                  经代业务管理系统
                </NuxtLink>
              </li>
            </ul>
          </div>

          <!-- 解决方案 -->
          <div class="text-center lg:w-1/6">
            <h4 class="font-semibold mb-6 text-primary-200 text-lg">
              解决方案
            </h4>
            <ul class="space-y-3 text-sm text-primary-400">
              <li>
                <NuxtLink
                  to="/solutions/insurance-company"
                  class="hover:text-primary-300 transition-colors cursor-pointer"
                >
                  保险公司解决方案
                </NuxtLink>
              </li>
              <li>
                <NuxtLink
                  to="/solutions/insurance-agency"
                  class="hover:text-primary-300 transition-colors cursor-pointer"
                >
                  保险中介解决方案
                </NuxtLink>
              </li>
              <li>
                <NuxtLink
                  to="/solutions/insurance-mga"
                  class="hover:text-primary-300 transition-colors cursor-pointer"
                >
                  保险 MGA 解决方案
                </NuxtLink>
              </li>
              <li>
                <NuxtLink
                  to="/solutions/logistics"
                  class="hover:text-primary-300 transition-colors cursor-pointer"
                >
                  物流行业解决方案
                </NuxtLink>
              </li>
            </ul>
          </div>

          <!-- 服务体系 -->
          <div class="text-center lg:w-1/6">
            <h4 class="font-semibold mb-6 text-primary-200 text-lg">
              服务体系
            </h4>
            <ul class="space-y-3 text-sm text-primary-400">
              <li>
                <NuxtLink
                  to="/services/digital-transformation"
                  class="hover:text-primary-300 transition-colors cursor-pointer"
                >
                  数字化转型咨询
                </NuxtLink>
              </li>
              <li>
                <NuxtLink
                  to="/services/it-compliance"
                  class="hover:text-primary-300 transition-colors cursor-pointer"
                >
                  IT 等保合规咨询
                </NuxtLink>
              </li>
              <li>
                <NuxtLink
                  to="/services/mga-operation"
                  class="hover:text-primary-300 transition-colors cursor-pointer"
                >
                  货运保险 MGA 运营
                </NuxtLink>
              </li>
              <li>
                <NuxtLink
                  to="/services/agency-operation"
                  class="hover:text-primary-300 transition-colors cursor-pointer"
                >
                  财产险中介机构运营
                </NuxtLink>
              </li>
              <li>
                <NuxtLink
                  to="/services/social-media-marketing"
                  class="hover:text-primary-300 transition-colors cursor-pointer"
                >
                  保险社交媒体营销运营
                </NuxtLink>
              </li>
              <li>
                <NuxtLink
                  to="/services/legal-consulting"
                  class="hover:text-primary-300 transition-colors cursor-pointer"
                >
                  货运保险法律咨询
                </NuxtLink>
              </li>
            </ul>
          </div>

          <!-- 更多链接 -->
          <div class="text-center lg:w-1/6">
            <h4 class="font-semibold mb-6 text-primary-200 text-lg">
              更多链接
            </h4>
            <ul class="space-y-3 text-sm text-primary-400">
              <li>
                <NuxtLink
                  to="/cases"
                  class="hover:text-primary-300 transition-colors cursor-pointer"
                >
                  案例中心
                </NuxtLink>
              </li>
              <li>
                <NuxtLink
                  to="/about"
                  class="hover:text-primary-300 transition-colors cursor-pointer"
                >
                  关于我们
                </NuxtLink>
              </li>
              <li>
                <NuxtLink
                  to="/products"
                  class="hover:text-primary-300 transition-colors cursor-pointer"
                >
                  产品体系
                </NuxtLink>
              </li>
              <li>
                <NuxtLink
                  to="/solutions"
                  class="hover:text-primary-300 transition-colors cursor-pointer"
                >
                  解决方案
                </NuxtLink>
              </li>
              <li>
                <NuxtLink
                  to="/services"
                  class="hover:text-primary-300 transition-colors cursor-pointer"
                >
                  服务体系
                </NuxtLink>
              </li>
            </ul>
          </div>
        </div>

        <!-- 页脚底部 -->
        <div
          class="text-xs border-t border-primary-800 pt-8 flex flex-col lg:flex-row justify-between items-center text-sm text-primary-400"
        >
          <p>© 2024 赢睿保险科技. 保留所有权利. | 沪ICP备19003849号-2</p>
          <div class="flex items-center space-x-6 mt-4 lg:mt-0">
            <span class="text-primary-400">AI 驱动 · 智慧保险 · 数字未来</span>
          </div>
        </div>
      </div>
    </footer>

    <!-- 回到顶部按钮 -->
    <button
      class="fixed bottom-8 right-8 z-50 back-to-top bg-primary-500 hover:bg-primary-600 text-white w-14 h-14 rounded-full flex items-center justify-center shadow-xl transition-all duration-300 opacity-0 invisible"
      @click="scrollToTop"
    >
      <UIcon name="i-heroicons-arrow-up" class="w-6 h-6" />
    </button>

    <!-- 立即咨询抽屉 -->
    <InquiryDrawer
      v-model="isInquiryDrawerOpen"
      @submit="handleInquirySubmit"
    />
  </div>
</template>

<script setup lang="ts">
// 菜单数据
const mainMenus = ref([
  {
    id: "products",
    name: "产品体系",
    link: "/products",
    children: [
      {
        title: "AI智能",
        items: [
          {
            id: "ai-risk-pricing",
            name: "AI风控定价系统",
            link: "/products/ai-risk-pricing",
            description: "AI大语言模型+行业大数据精准定价",
            icon: "i-heroicons-chart-bar",
          },
          {
            id: "ai-claims",
            name: "AI理赔管理系统",
            link: "/products/ai-claims",
            description: "图像识别+机器学习+欺诈检测",
            icon: "i-heroicons-document-check",
          },
          {
            id: "ai-customer-service",
            name: "AI客服管理系统",
            link: "/products/ai-customer-service",
            description: "自然语言处理+智能对话",
            icon: "i-heroicons-user-group",
          },
          {
            id: "ai-knowledge-base",
            name: "AI企业知识库",
            link: "/products/ai-knowledge-base",
            description: "智能检索+文档整理+案例匹配",
            icon: "i-heroicons-academic-cap",
          },
        ],
      },
      {
        title: "核心系统",
        items: [
          {
            id: "core-business-platform",
            name: "保险综合业务中台",
            link: "/products/core-business-platform",
            description: "AI赋能的业务中台系统",
            icon: "i-heroicons-server",
          },
          {
            id: "agency-management",
            name: "经代业务管理系统",
            link: "/products/agency-management",
            description: "专业的中介机构业务管理平台",
            icon: "i-heroicons-building-storefront",
          },
          {
            id: "product-management",
            name: "保险产品管理系统",
            link: "/products/product-management",
            description: "全生命周期产品管理平台",
            icon: "i-heroicons-cube",
          },
          {
            id: "multi-end-underwriting",
            name: "多端综合核保系统",
            link: "/products/multi-end-underwriting",
            description: "全渠道智能核保平台",
            icon: "i-heroicons-clipboard-document-check",
          },
          {
            id: "api-platform",
            name: "企业级API开放平台",
            link: "/products/api-platform",
            description: "标准化API接口服务",
            icon: "i-heroicons-code-bracket",
          },
          {
            id: "claims-management",
            name: "理赔综合管理系统",
            link: "/products/claims-management",
            description: "全流程理赔自动化处理",
            icon: "i-heroicons-document-text",
          },
          {
            id: "automation-platform",
            name: "多端自动化服务平台",
            link: "/products/automation-platform",
            description: "业务流程自动化处理",
            icon: "i-heroicons-cog-6-tooth",
          },
          {
            id: "bi-analytics",
            name: "BI数据智能分析系统",
            link: "/products/bi-analytics",
            description: "智能数据分析与决策支持",
            icon: "i-heroicons-presentation-chart-line",
          },
          {
            id: "payment-platform",
            name: "保费在线整合支付平台",
            link: "/products/payment-platform",
            description: "多渠道支付整合服务",
            icon: "i-heroicons-credit-card",
          },
        ],
      },
      {
        title: "营销获客",
        items: [
          {
            id: "content-management",
            name: "多端分发营销内容管理系统",
            link: "/products/content-management",
            description: "智能内容创作与分发",
            icon: "i-heroicons-squares-2x2",
          },
          {
            id: "message-distribution",
            name: "全平台消息分发系统",
            link: "/products/message-distribution",
            description: "多渠道消息统一管理",
            icon: "i-heroicons-envelope",
          },
          {
            id: "scrm-system",
            name: "SCRM社媒运营系统",
            link: "/products/scrm-system",
            description: "社交媒体客户关系管理",
            icon: "i-heroicons-megaphone",
          },
        ],
      },
      {
        title: "IT基建",
        items: [
          {
            id: "devops-system",
            name: "DevOps研运体系",
            link: "/products/devops-system",
            description: "开发运维一体化平台",
            icon: "i-heroicons-command-line",
          },
          {
            id: "project-management",
            name: "一体化项目管理平台",
            link: "/products/project-management",
            description: "全流程项目管理工具",
            icon: "i-heroicons-clipboard-document-list",
          },
          {
            id: "monitoring-platform",
            name: "运维监控平台",
            link: "/products/monitoring-platform",
            description: "系统监控与运维管理",
            icon: "i-heroicons-eye",
          },
          {
            id: "it-risk-management",
            name: "IT风控管理体系",
            link: "/products/it-risk-management",
            description: "IT风险控制与管理",
            icon: "i-heroicons-shield-exclamation",
          },
        ],
      },
    ],
  },
  {
    id: "solutions",
    name: "解决方案",
    link: "/solutions",
    children: [
      {
        title: "行业解决方案",
        items: [
          {
            id: "insurance-company",
            name: "保险公司解决方案",
            link: "/solutions/insurance-company",
            description: "AI驱动的保险公司数字化转型方案",
            icon: "i-heroicons-building-office",
          },
          {
            id: "insurance-agency",
            name: "保险中介解决方案",
            link: "/solutions/insurance-agency",
            description: "中介机构AI赋能与效率提升方案",
            icon: "i-heroicons-users",
          },
          {
            id: "insurance-mga",
            name: "保险MGA解决方案",
            link: "/solutions/insurance-mga",
            description: "专业MGA的AI智能化运营方案",
            icon: "i-heroicons-chart-pie",
          },
          {
            id: "logistics",
            name: "物流行业解决方案",
            link: "/solutions/logistics",
            description: "货运保险AI创新应用方案",
            icon: "i-heroicons-truck",
          },
        ],
      },
    ],
  },
  {
    id: "services",
    name: "服务体系",
    link: "/services",
    children: [
      {
        title: "咨询服务",
        items: [
          {
            id: "digital-transformation",
            name: "保险机构数字化转型咨询",
            link: "/services/digital-transformation",
            description: "专业的数字化转型规划与实施",
            icon: "i-heroicons-light-bulb",
          },
          {
            id: "it-compliance",
            name: "保险机构IT等保合规咨询",
            link: "/services/it-compliance",
            description: "信息系统等级保护合规服务",
            icon: "i-heroicons-shield-check",
          },
          {
            id: "legal-consulting",
            name: "货运保险法律咨询",
            link: "/services/legal-consulting",
            description: "专业的货运保险法律服务",
            icon: "i-heroicons-scale",
          },
        ],
      },
      {
        title: "运营服务",
        items: [
          {
            id: "mga-operation",
            name: "货运保险MGA运营",
            link: "/services/mga-operation",
            description: "专业的MGA运营管理服务",
            icon: "i-heroicons-cog-6-tooth",
          },
          {
            id: "agency-operation",
            name: "财产险中介机构运营",
            link: "/services/agency-operation",
            description: "中介机构全流程运营支持",
            icon: "i-heroicons-user-group",
          },
          {
            id: "social-media-marketing",
            name: "保险社交媒体营销运营",
            link: "/services/social-media-marketing",
            description: "专业的社媒营销策划与执行",
            icon: "i-heroicons-megaphone",
          },
        ],
      },
    ],
  },
  {
    id: "cases",
    name: "案例中心",
    link: "/cases",
  },
  {
    id: "about",
    name: "关于我们",
    link: "/about",
  },
]);

// 当前激活的菜单
const activeMenu = ref<string | null>(null);

// 移动端菜单状态
const isMobileMenuOpen = useState("mobileMenu", () => false);
const activeMobileMenu = useState<string | null>(
  "activeMobileMenu",
  () => null
);

// 全局咨询抽屉状态
const isInquiryDrawerOpen = useState("inquiryDrawer", () => false);

// 获取当前激活菜单的子菜单数据
const getActiveMenuData = () => {
  if (!activeMenu.value) return null;
  const menu = mainMenus.value.find((m) => m.id === activeMenu.value);
  return menu?.children || null;
};

// 处理header鼠标离开事件
const handleHeaderMouseLeave = () => {
  activeMenu.value = null;
};

// 关闭subnav的函数
const closeSubnav = () => {
  activeMenu.value = null;
};

// 打开咨询抽屉
const openInquiryDrawer = () => {
  closeSubnav(); // 先关闭子菜单
  isInquiryDrawerOpen.value = true;
  console.log("isInquiryDrawerOpen:", isInquiryDrawerOpen.value);
};

// 提供全局打开咨询抽屉的方法
provide("openInquiryDrawer", openInquiryDrawer);

// 切换移动端菜单
const toggleMobileMenu = () => {
  isMobileMenuOpen.value = !isMobileMenuOpen.value;
  activeMobileMenu.value = null; // 关闭子菜单
};

// 关闭移动端菜单
const closeMobileMenu = () => {
  isMobileMenuOpen.value = false;
  activeMobileMenu.value = null;
};

// 切换移动端子菜单
const toggleMobileSubmenu = (menuId: string) => {
  if (activeMobileMenu.value === menuId) {
    activeMobileMenu.value = null;
  } else {
    activeMobileMenu.value = menuId;
  }
};

// 键盘导航支持
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === "Escape" && isMobileMenuOpen.value) {
    closeMobileMenu();
  }
};

// 监听键盘事件
onMounted(() => {
  document.addEventListener("keydown", handleKeydown);

  onUnmounted(() => {
    document.removeEventListener("keydown", handleKeydown);
  });
});

// 处理咨询表单提交
const handleInquirySubmit = (data: any) => {
  console.log("收到咨询表单数据:", data);

  // 这里可以添加实际的提交逻辑
  // 例如：发送到后端API、保存到数据库等

  // 可以添加统计埋点
  // analytics.track('inquiry_submitted', data);

  // 可以触发其他业务逻辑
  // 例如：发送邮件通知、创建客户记录等
};

// 回到顶部功能
const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: "smooth",
  });
};

// 监听滚动事件显示/隐藏回到顶部按钮
onMounted(() => {
  const backToTopBtn = document.querySelector(".back-to-top");

  const handleScroll = () => {
    if (window.scrollY > 500) {
      backToTopBtn?.classList.add("opacity-100", "visible");
      backToTopBtn?.classList.remove("opacity-0", "invisible");
    } else {
      backToTopBtn?.classList.remove("opacity-100", "visible");
      backToTopBtn?.classList.add("opacity-0", "invisible");
    }
  };

  window.addEventListener("scroll", handleScroll);

  // 监听路由变化，关闭移动端菜单
  const router = useRouter();
  router.afterEach(() => {
    closeMobileMenu();
  });

  onUnmounted(() => {
    window.removeEventListener("scroll", handleScroll);
  });
});
</script>

<style scoped>
/* 平滑滚动和动画效果 */
html {
  scroll-behavior: smooth;
}

.back-to-top {
  transition: all 0.3s ease-in-out;
}

.back-to-top:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(16, 185, 129, 0.4);
}

/* 菜单动画效果 */
.menu-item {
  transition: all 0.3s ease-in-out;
}

.menu-item:hover {
  transform: translateY(-2px);
}

/* 主菜单active效果 */
.main-menu-active {
  position: relative;
}

.main-menu-active::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  background: linear-gradient(90deg, #10b981, #059669);
  border-radius: 1px;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    width: 0;
    opacity: 0;
  }
  to {
    width: 20px;
    opacity: 1;
  }
}

.subnav-enter-active,
.subnav-leave-active {
  transition: all 0.3s ease-in-out;
}

.subnav-enter-from,
.subnav-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* 移动端菜单动画效果 */
.mobile-menu-enter-active,
.mobile-menu-leave-active {
  transition: all 0.3s ease-in-out;
}

.mobile-menu-enter-from,
.mobile-menu-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

.mobile-submenu-enter-active,
.mobile-submenu-leave-active {
  transition: all 0.3s ease-in-out;
}

.mobile-submenu-enter-from,
.mobile-submenu-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* 响应式优化 */
@media (max-width: 768px) {
  .back-to-top {
    width: 3rem;
    height: 3rem;
    bottom: 1.5rem;
    right: 1.5rem;
  }

  /* 移动端菜单优化 */
  .mobile-menu-item {
    border-radius: 0.5rem;
  }

  /* 确保移动端菜单内容可滚动 */
  .mobile-menu-content {
    -webkit-overflow-scrolling: touch;
  }
}

/* 移动端菜单遮罩优化 */
@media (max-width: 640px) {
  .mobile-menu-overlay {
    backdrop-filter: blur(4px);
  }

  /* 小屏幕设备菜单宽度调整 */
  .mobile-menu-content {
    width: 100%;
    max-width: 320px;
  }
}

/* 确保移动端菜单在横屏模式下也能正常显示 */
@media (max-height: 500px) and (orientation: landscape) {
  .mobile-menu-content {
    height: 100vh;
    overflow-y: auto;
  }
}
</style>
