<template>
  <div class="min-h-screen bg-white">
    <!-- Hero Section -->
    <section
      class="relative overflow-hidden bg-primary-900 pt-32 lg:pt-60 pb-20 lg:pb-28"
    >
      <div class="relative container mx-auto px-6">
        <div class="max-w-5xl mx-auto text-center text-white">
          <!-- 面包屑导航 -->
          <nav class="mb-8">
            <ol
              class="flex justify-center items-center space-x-2 text-sm text-primary-300"
            >
              <li>
                <NuxtLink
                  to="/"
                  class="hover:text-primary-400 transition-colors"
                >
                  首页
                </NuxtLink>
              </li>
              <li>
                <UIcon name="i-heroicons-chevron-right" class="w-4 h-4" />
              </li>
              <li>
                <NuxtLink
                  to="/products"
                  class="hover:text-primary-400 transition-colors"
                >
                  产品体系
                </NuxtLink>
              </li>
              <li>
                <UIcon name="i-heroicons-chevron-right" class="w-4 h-4" />
              </li>
              <li class="text-primary-400">BI数据智能分析系统</li>
            </ol>
          </nav>

          <h1
            class="text-4xl lg:text-6xl font-bold mb-8 leading-tight animate-fade-in-up"
            style="animation-delay: 0.2s"
          >
            <span class="text-primary-500">BI数据</span> 智能分析系统
            <span
              class="block text-2xl lg:text-3xl font-light mt-4 text-primary-200"
            >
              智能数据分析与决策支持平台
            </span>
          </h1>
        </div>
      </div>
    </section>

    <!-- 产品特点 -->
    <section class="py-20 lg:py-28 bg-primary-50">
      <div class="container mx-auto px-6">
        <div class="text-center mb-16">
          <h2 class="text-4xl lg:text-5xl font-bold text-slate-900 mb-6">
            产品核心特点
          </h2>
          <p class="text-xl text-slate-600 max-w-4xl mx-auto font-light">
            智能数据分析，深度洞察业务，助力精准决策
          </p>
        </div>

        <div class="grid lg:grid-cols-3 md:grid-cols-2 gap-8 max-w-7xl mx-auto">
          <!-- 智能分析 -->
          <div
            class="bg-white rounded-2xl p-8 hover:shadow-xl transition-all duration-300 group"
          >
            <div
              class="w-16 h-16 bg-primary-500 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300"
            >
              <UIcon name="i-heroicons-brain" class="w-8 h-8 text-white" />
            </div>
            <h3 class="text-2xl font-bold text-slate-900 mb-4">智能分析</h3>
            <p class="text-slate-600 leading-relaxed">
              AI驱动的智能分析引擎，自动发现数据规律，提供深度业务洞察
            </p>
          </div>

          <!-- 实时监控 -->
          <div
            class="bg-white rounded-2xl p-8 hover:shadow-xl transition-all duration-300 group"
          >
            <div
              class="w-16 h-16 bg-primary-500 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300"
            >
              <UIcon name="i-heroicons-eye" class="w-8 h-8 text-white" />
            </div>
            <h3 class="text-2xl font-bold text-slate-900 mb-4">实时监控</h3>
            <p class="text-slate-600 leading-relaxed">
              实时监控业务数据，及时发现异常，提供预警和决策建议
            </p>
          </div>

          <!-- 可视化展示 -->
          <div
            class="bg-white rounded-2xl p-8 hover:shadow-xl transition-all duration-300 group"
          >
            <div
              class="w-16 h-16 bg-primary-500 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300"
            >
              <UIcon
                name="i-heroicons-presentation-chart-line"
                class="w-8 h-8 text-white"
              />
            </div>
            <h3 class="text-2xl font-bold text-slate-900 mb-4">可视化展示</h3>
            <p class="text-slate-600 leading-relaxed">
              丰富的图表类型，直观展示数据趋势，支持多维度数据钻取
            </p>
          </div>

          <!-- 预测分析 -->
          <div
            class="bg-white rounded-2xl p-8 hover:shadow-xl transition-all duration-300 group"
          >
            <div
              class="w-16 h-16 bg-primary-500 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300"
            >
              <UIcon name="i-heroicons-chart-bar" class="w-8 h-8 text-white" />
            </div>
            <h3 class="text-2xl font-bold text-slate-900 mb-4">预测分析</h3>
            <p class="text-slate-600 leading-relaxed">
              基于历史数据的预测模型，为业务决策提供科学依据
            </p>
          </div>

          <!-- 多源数据 -->
          <div
            class="bg-white rounded-2xl p-8 hover:shadow-xl transition-all duration-300 group"
          >
            <div
              class="w-16 h-16 bg-primary-500 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300"
            >
              <UIcon name="i-heroicons-database" class="w-8 h-8 text-white" />
            </div>
            <h3 class="text-2xl font-bold text-slate-900 mb-4">多源数据</h3>
            <p class="text-slate-600 leading-relaxed">
              支持多种数据源接入，统一数据管理，确保数据一致性
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- 核心功能 -->
    <section class="py-20 lg:py-28 bg-white">
      <div class="container mx-auto px-6">
        <div class="text-center mb-16">
          <h2 class="text-4xl lg:text-5xl font-bold text-slate-900 mb-6">
            核心功能介绍
          </h2>
          <p class="text-xl text-slate-600 max-w-4xl mx-auto font-light">
            四大核心功能模块，全方位提升您的数据分析能力
          </p>
        </div>

        <div class="space-y-20 max-w-7xl mx-auto">
          <!-- 智能数据挖掘 -->
          <div class="flex flex-col lg:flex-row items-center gap-12">
            <div class="w-full lg:w-1/2">
              <img
                src="/img/photos/laptop-3196481_640.jpg"
                alt="智能数据挖掘"
                class="w-full h-80 object-cover rounded-2xl shadow-lg"
              />
            </div>
            <div class="w-full lg:w-1/2">
              <div
                class="w-16 h-16 bg-primary-500 rounded-2xl flex items-center justify-center mb-6"
              >
                <UIcon
                  name="i-heroicons-magnifying-glass"
                  class="w-8 h-8 text-white"
                />
              </div>
              <h3 class="text-3xl font-bold text-slate-900 mb-4">
                智能数据挖掘
              </h3>
              <p class="text-lg text-slate-600 mb-6 leading-relaxed">
                AI驱动的数据挖掘引擎，自动发现数据中的隐藏模式和关联关系。支持多维度数据分析，深度挖掘业务价值，为决策提供科学依据。
              </p>
              <div class="space-y-3 mb-8">
                <div class="flex items-start space-x-3">
                  <div
                    class="w-2 h-2 bg-primary-500 rounded-full mt-2.5 flex-shrink-0"
                  ></div>
                  <span class="text-slate-600">AI驱动的数据挖掘引擎</span>
                </div>
                <div class="flex items-start space-x-3">
                  <div
                    class="w-2 h-2 bg-primary-500 rounded-full mt-2.5 flex-shrink-0"
                  ></div>
                  <span class="text-slate-600">自动发现隐藏模式和关联关系</span>
                </div>
                <div class="flex items-start space-x-3">
                  <div
                    class="w-2 h-2 bg-primary-500 rounded-full mt-2.5 flex-shrink-0"
                  ></div>
                  <span class="text-slate-600">多维度数据分析</span>
                </div>
              </div>
              <UButton
                class="bg-primary-500 hover:bg-primary-600 text-white px-8 py-3"
                @click="openInquiryDrawer"
              >
                立即咨询
                <UIcon name="i-heroicons-arrow-right" class="w-4 h-4 ml-1" />
              </UButton>
            </div>
          </div>

          <!-- 实时监控预警 -->
          <div class="flex flex-col lg:flex-row-reverse items-center gap-12">
            <div class="w-full lg:w-1/2">
              <img
                src="/img/photos/office-620822_640.jpg"
                alt="实时监控预警"
                class="w-full h-80 object-cover rounded-2xl shadow-lg"
              />
            </div>
            <div class="w-full lg:w-1/2">
              <div
                class="w-16 h-16 bg-primary-500 rounded-2xl flex items-center justify-center mb-6"
              >
                <UIcon
                  name="i-heroicons-bell-alert"
                  class="w-8 h-8 text-white"
                />
              </div>
              <h3 class="text-3xl font-bold text-slate-900 mb-4">
                实时监控预警
              </h3>
              <p class="text-lg text-slate-600 mb-6 leading-relaxed">
                实时监控业务数据变化，智能识别异常情况，及时发送预警通知。支持自定义预警规则，确保关键指标得到及时关注。
              </p>
              <div class="space-y-3 mb-8">
                <div class="flex items-start space-x-3">
                  <div
                    class="w-2 h-2 bg-primary-500 rounded-full mt-2.5 flex-shrink-0"
                  ></div>
                  <span class="text-slate-600">实时监控业务数据变化</span>
                </div>
                <div class="flex items-start space-x-3">
                  <div
                    class="w-2 h-2 bg-primary-500 rounded-full mt-2.5 flex-shrink-0"
                  ></div>
                  <span class="text-slate-600">智能识别异常情况</span>
                </div>
                <div class="flex items-start space-x-3">
                  <div
                    class="w-2 h-2 bg-primary-500 rounded-full mt-2.5 flex-shrink-0"
                  ></div>
                  <span class="text-slate-600">自定义预警规则</span>
                </div>
              </div>
              <UButton
                class="bg-primary-500 hover:bg-primary-600 text-white px-8 py-3"
              >
                了解详情 →
              </UButton>
            </div>
          </div>

          <!-- 可视化报表 -->
          <div class="flex flex-col lg:flex-row items-center gap-12">
            <div class="w-full lg:w-1/2">
              <img
                src="/img/photos/meeting-2284501_640.jpg"
                alt="可视化报表"
                class="w-full h-80 object-cover rounded-2xl shadow-lg"
              />
            </div>
            <div class="w-full lg:w-1/2">
              <div
                class="w-16 h-16 bg-primary-500 rounded-2xl flex items-center justify-center mb-6"
              >
                <UIcon
                  name="i-heroicons-document-chart-bar"
                  class="w-8 h-8 text-white"
                />
              </div>
              <h3 class="text-3xl font-bold text-slate-900 mb-4">可视化报表</h3>
              <p class="text-lg text-slate-600 mb-6 leading-relaxed">
                丰富的图表类型和报表模板，直观展示数据趋势和业务洞察。支持多维度数据钻取，深度分析业务数据。
              </p>
              <div class="space-y-3 mb-8">
                <div class="flex items-start space-x-3">
                  <div
                    class="w-2 h-2 bg-primary-500 rounded-full mt-2.5 flex-shrink-0"
                  ></div>
                  <span class="text-slate-600">丰富的图表类型和报表模板</span>
                </div>
                <div class="flex items-start space-x-3">
                  <div
                    class="w-2 h-2 bg-primary-500 rounded-full mt-2.5 flex-shrink-0"
                  ></div>
                  <span class="text-slate-600">直观展示数据趋势</span>
                </div>
                <div class="flex items-start space-x-3">
                  <div
                    class="w-2 h-2 bg-primary-500 rounded-full mt-2.5 flex-shrink-0"
                  ></div>
                  <span class="text-slate-600">支持多维度数据钻取</span>
                </div>
              </div>
              <UButton
                class="bg-primary-500 hover:bg-primary-600 text-white px-8 py-3"
              >
                了解详情 →
              </UButton>
            </div>
          </div>

          <!-- 预测分析模型 -->
          <div class="flex flex-col lg:flex-row-reverse items-center gap-12">
            <div class="w-full lg:w-1/2">
              <img
                src="/img/photos/man-593333_640.jpg"
                alt="预测分析模型"
                class="w-full h-80 object-cover rounded-2xl shadow-lg"
              />
            </div>
            <div class="w-full lg:w-1/2">
              <div
                class="w-16 h-16 bg-primary-500 rounded-2xl flex items-center justify-center mb-6"
              >
                <UIcon
                  name="i-heroicons-chart-pie"
                  class="w-8 h-8 text-white"
                />
              </div>
              <h3 class="text-3xl font-bold text-slate-900 mb-4">
                预测分析模型
              </h3>
              <p class="text-lg text-slate-600 mb-6 leading-relaxed">
                基于机器学习的预测分析模型，准确预测业务趋势和风险。支持多种预测算法，为业务决策提供科学依据。
              </p>
              <div class="space-y-3 mb-8">
                <div class="flex items-start space-x-3">
                  <div
                    class="w-2 h-2 bg-primary-500 rounded-full mt-2.5 flex-shrink-0"
                  ></div>
                  <span class="text-slate-600">基于机器学习的预测模型</span>
                </div>
                <div class="flex items-start space-x-3">
                  <div
                    class="w-2 h-2 bg-primary-500 rounded-full mt-2.5 flex-shrink-0"
                  ></div>
                  <span class="text-slate-600">准确预测业务趋势和风险</span>
                </div>
                <div class="flex items-start space-x-3">
                  <div
                    class="w-2 h-2 bg-primary-500 rounded-full mt-2.5 flex-shrink-0"
                  ></div>
                  <span class="text-slate-600">支持多种预测算法</span>
                </div>
              </div>
              <UButton
                class="bg-primary-500 hover:bg-primary-600 text-white px-8 py-3"
              >
                了解详情 →
              </UButton>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 应用场景 -->
    <section class="py-20 lg:py-28 bg-primary-50">
      <div class="container mx-auto px-6">
        <div class="text-center mb-16">
          <h2 class="text-4xl lg:text-5xl font-bold text-slate-900 mb-6">
            应用场景介绍
          </h2>
          <p class="text-xl text-slate-600 max-w-4xl mx-auto font-light">
            适用于多种业务场景，满足不同规模机构的数据分析需求
          </p>
        </div>

        <div class="grid lg:grid-cols-2 gap-12 max-w-7xl mx-auto">
          <!-- 保险业务分析 -->
          <div
            class="bg-white rounded-3xl p-8 border border-primary-200 hover:shadow-2xl transition-all duration-300 group"
          >
            <div class="flex items-start space-x-6">
              <div class="relative">
                <img
                  src="/img/photos/desk-3139127_640.jpg"
                  alt="保险业务分析"
                  class="w-24 h-24 object-cover rounded-2xl"
                />
                <div
                  class="absolute -bottom-2 -right-2 w-10 h-10 bg-primary-500 rounded-xl flex items-center justify-center"
                >
                  <UIcon
                    name="i-heroicons-building-office"
                    class="w-5 h-5 text-white"
                  />
                </div>
              </div>
              <div class="flex-1">
                <h3
                  class="text-2xl font-bold text-slate-900 mb-3 group-hover:text-slate-600 transition-colors"
                >
                  保险业务分析
                </h3>
                <p class="text-slate-600 mb-4 leading-relaxed">
                  深度分析保险业务数据，包括承保、理赔、客户等关键指标，为业务决策提供数据支撑。
                </p>
                <div class="space-y-2 mb-6">
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span><strong>承保分析：</strong>承保质量和效率分析</span>
                  </div>
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span><strong>理赔分析：</strong>理赔趋势和风险分析</span>
                  </div>
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span><strong>客户分析：</strong>客户行为和满意度分析</span>
                  </div>
                </div>
                <UButton
                  variant="outline"
                  color="neutral"
                  class="group-hover:bg-primary-500 group-hover:text-white transition-all duration-300"
                >
                  立即咨询 →
                </UButton>
              </div>
            </div>
          </div>

          <!-- 金融风险监控 -->
          <div
            class="bg-white rounded-3xl p-8 border border-primary-200 hover:shadow-2xl transition-all duration-300 group"
          >
            <div class="flex items-start space-x-6">
              <div class="relative">
                <img
                  src="/img/photos/job-5382501_640.jpg"
                  alt="金融风险监控"
                  class="w-24 h-24 object-cover rounded-2xl"
                />
                <div
                  class="absolute -bottom-2 -right-2 w-10 h-10 bg-primary-500 rounded-xl flex items-center justify-center"
                >
                  <UIcon
                    name="i-heroicons-shield-exclamation"
                    class="w-5 h-5 text-white"
                  />
                </div>
              </div>
              <div class="flex-1">
                <h3
                  class="text-2xl font-bold text-slate-900 mb-3 group-hover:text-slate-600 transition-colors"
                >
                  金融风险监控
                </h3>
                <p class="text-slate-600 mb-4 leading-relaxed">
                  实时监控金融风险指标，智能识别风险信号，及时预警和处置，保障业务安全。
                </p>
                <div class="space-y-2 mb-6">
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span><strong>风险识别：</strong>智能识别风险信号</span>
                  </div>
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span><strong>实时监控：</strong>24小时风险监控</span>
                  </div>
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span><strong>预警处置：</strong>及时预警和处置</span>
                  </div>
                </div>
                <UButton
                  variant="outline"
                  color="neutral"
                  class="group-hover:bg-primary-500 group-hover:text-white transition-all duration-300"
                >
                  立即咨询 →
                </UButton>
              </div>
            </div>
          </div>

          <!-- 企业运营分析 -->
          <div
            class="bg-white rounded-3xl p-8 border border-primary-200 hover:shadow-2xl transition-all duration-300 group"
          >
            <div class="flex items-start space-x-6">
              <div class="relative">
                <img
                  src="/img/photos/laptops-593296_640.jpg"
                  alt="企业运营分析"
                  class="w-24 h-24 object-cover rounded-2xl"
                />
                <div
                  class="absolute -bottom-2 -right-2 w-10 h-10 bg-primary-500 rounded-xl flex items-center justify-center"
                >
                  <UIcon
                    name="i-heroicons-building-office-2"
                    class="w-5 h-5 text-white"
                  />
                </div>
              </div>
              <div class="flex-1">
                <h3
                  class="text-2xl font-bold text-slate-900 mb-3 group-hover:text-slate-600 transition-colors"
                >
                  企业运营分析
                </h3>
                <p class="text-slate-600 mb-4 leading-relaxed">
                  全面分析企业运营数据，包括销售、财务、人力资源等，优化运营效率，提升企业竞争力。
                </p>
                <div class="space-y-2 mb-6">
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span><strong>销售分析：</strong>销售趋势和业绩分析</span>
                  </div>
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span><strong>财务分析：</strong>财务状况和盈利能力</span>
                  </div>
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span><strong>人力资源：</strong>人员效率和成本分析</span>
                  </div>
                </div>
                <UButton
                  variant="outline"
                  color="neutral"
                  class="group-hover:bg-primary-500 group-hover:text-white transition-all duration-300"
                >
                  立即咨询 →
                </UButton>
              </div>
            </div>
          </div>

          <!-- 市场趋势预测 -->
          <div
            class="bg-white rounded-3xl p-8 border border-primary-200 hover:shadow-2xl transition-all duration-300 group"
          >
            <div class="flex items-start space-x-6">
              <div class="relative">
                <img
                  src="/img/photos/meeting-2284501_640.jpg"
                  alt="市场趋势预测"
                  class="w-24 h-24 object-cover rounded-2xl"
                />
                <div
                  class="absolute -bottom-2 -right-2 w-10 h-10 bg-primary-500 rounded-xl flex items-center justify-center"
                >
                  <UIcon
                    name="i-heroicons-trending-up"
                    class="w-5 h-5 text-white"
                  />
                </div>
              </div>
              <div class="flex-1">
                <h3
                  class="text-2xl font-bold text-slate-900 mb-3 group-hover:text-slate-600 transition-colors"
                >
                  市场趋势预测
                </h3>
                <p class="text-slate-600 mb-4 leading-relaxed">
                  基于历史数据和市场信息，预测市场趋势和客户需求，为产品开发和营销策略提供指导。
                </p>
                <div class="space-y-2 mb-6">
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span><strong>趋势预测：</strong>市场趋势预测</span>
                  </div>
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span><strong>需求分析：</strong>客户需求预测</span>
                  </div>
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span><strong>策略指导：</strong>产品开发和营销策略</span>
                  </div>
                </div>
                <UButton
                  variant="outline"
                  color="neutral"
                  class="group-hover:bg-primary-500 group-hover:text-white transition-all duration-300"
                >
                  立即咨询 →
                </UButton>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 产品优势 -->
    <section
      class="py-20 lg:py-28 bg-primary-900 text-white relative overflow-hidden"
    >
      <div class="container mx-auto px-6 relative">
        <div class="text-center mb-16">
          <h2 class="text-4xl lg:text-5xl font-bold mb-6">
            为什么选择赢睿 <span class="text-primary-400">BI分析</span> 系统
          </h2>
          <p class="text-xl text-primary-300 max-w-4xl mx-auto font-light">
            数据驱动的智能分析，助力您实现精准决策
          </p>
        </div>

        <div class="grid lg:grid-cols-4 md:grid-cols-2 gap-8 max-w-6xl mx-auto">
          <div
            class="text-center bg-primary-800/50 backdrop-blur rounded-2xl p-8 border border-primary-700"
          >
            <div class="text-4xl lg:text-5xl font-bold text-primary-400 mb-4">
              95%
            </div>
            <div class="text-primary-300 text-lg font-medium mb-2">准确率</div>
            <div class="text-sm text-primary-400">预测分析准确率</div>
          </div>

          <div
            class="text-center bg-primary-800/50 backdrop-blur rounded-2xl p-8 border border-primary-700"
          >
            <div class="text-4xl lg:text-5xl font-bold text-primary-400 mb-4">
              实时
            </div>
            <div class="text-primary-300 text-lg font-medium mb-2">
              数据监控
            </div>
            <div class="text-sm text-primary-400">24小时实时监控</div>
          </div>

          <div
            class="text-center bg-primary-800/50 backdrop-blur rounded-2xl p-8 border border-primary-700"
          >
            <div class="text-4xl lg:text-5xl font-bold text-primary-400 mb-4">
              50+
            </div>
            <div class="text-primary-300 text-lg font-medium mb-2">
              图表类型
            </div>
            <div class="text-sm text-primary-400">丰富的可视化图表</div>
          </div>

          <div
            class="text-center bg-primary-800/50 backdrop-blur rounded-2xl p-8 border border-primary-700"
          >
            <div class="text-4xl lg:text-5xl font-bold text-primary-400 mb-4">
              分钟级
            </div>
            <div class="text-primary-300 text-lg font-medium mb-2">
              快速部署
            </div>
            <div class="text-sm text-primary-400">即刻享受智能分析</div>
          </div>
        </div>
      </div>
    </section>
  </div>
  <!-- 全局咨询抽屉 -->
  <InquiryDrawer v-model="showInquiryDrawer" />
</template>

<script setup lang="ts">
// SEO 配置
useHead({
  title: "BI数据智能分析系统 - 赢睿保险科技",
  meta: [
    {
      name: "description",
      content:
        "智能数据分析与决策支持平台，AI驱动的数据挖掘，实时监控预警，可视化报表展示，预测分析准确率达95%。",
    },
    {
      property: "og:title",
      content: "BI数据智能分析系统 - 赢睿保险科技",
    },
    {
      property: "og:description",
      content: "智能数据分析与决策支持平台，AI驱动的数据挖掘，实时监控预警。",
    },
  ],
});

// 咨询抽屉状态
const showInquiryDrawer = ref(false);

// 打开咨询抽屉
const openInquiryDrawer = () => {
  showInquiryDrawer.value = true;
};

// 结构化数据
const structuredData = {
  "@context": "https://schema.org",
  "@type": "SoftwareApplication",
  name: "BI数据智能分析系统",
  description: "智能数据分析与决策支持平台，AI驱动的数据挖掘，实时监控预警",
  applicationCategory: "BusinessApplication",
  operatingSystem: "Web",
  offers: {
    "@type": "Offer",
    price: "0",
    priceCurrency: "CNY",
  },
};

useHead({
  script: [
    {
      type: "application/ld+json",
      innerHTML: JSON.stringify(structuredData),
    },
  ],
});

// 事件处理
const handleDemoClick = () => {
  // 处理演示预约
  console.log("预约产品演示");
};

const handleContactClick = () => {
  // 处理联系咨询
  console.log("联系咨询");
};
</script>

<style scoped>
.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
