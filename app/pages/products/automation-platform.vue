<template>
  <div class="min-h-screen bg-white">
    <!-- Hero Section -->
    <section
      class="relative overflow-hidden bg-primary-900 pt-32 lg:pt-60 pb-20 lg:pb-28"
    >
      <div class="relative container mx-auto px-6">
        <div class="max-w-5xl mx-auto text-center text-white">
          <!-- 面包屑导航 -->
          <nav class="mb-8">
            <ol
              class="flex justify-center items-center space-x-2 text-sm text-primary-300"
            >
              <li>
                <NuxtLink
                  to="/"
                  class="hover:text-primary-400 transition-colors"
                >
                  首页
                </NuxtLink>
              </li>
              <li>
                <UIcon name="i-heroicons-chevron-right" class="w-4 h-4" />
              </li>
              <li>
                <NuxtLink
                  to="/products"
                  class="hover:text-primary-400 transition-colors"
                >
                  产品体系
                </NuxtLink>
              </li>
              <li>
                <UIcon name="i-heroicons-chevron-right" class="w-4 h-4" />
              </li>
              <li class="text-primary-400">多端自动化服务平台</li>
            </ol>
          </nav>

          <h1
            class="text-4xl lg:text-6xl font-bold mb-8 leading-tight animate-fade-in-up"
            style="animation-delay: 0.2s"
          >
            <span class="text-primary-500">多端自动化</span> 服务平台
            <span
              class="block text-2xl lg:text-3xl font-light mt-4 text-primary-200"
            >
              业务流程自动化处理平台
            </span>
          </h1>
        </div>
      </div>
    </section>

    <!-- 产品特点 -->
    <section class="py-20 lg:py-28 bg-primary-50">
      <div class="container mx-auto px-6">
        <div class="text-center mb-16">
          <h2 class="text-4xl lg:text-5xl font-bold text-slate-900 mb-6">
            产品核心特点
          </h2>
          <p class="text-xl text-slate-600 max-w-4xl mx-auto font-light">
            全流程自动化处理，提升效率，降低成本，优化客户体验
          </p>
        </div>

        <div class="grid lg:grid-cols-3 md:grid-cols-2 gap-8 max-w-7xl mx-auto">
          <!-- 全流程自动化 -->
          <div
            class="bg-white rounded-2xl p-8 hover:shadow-xl transition-all duration-300 group"
          >
            <div
              class="w-16 h-16 bg-primary-500 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300"
            >
              <UIcon
                name="i-heroicons-cog-6-tooth"
                class="w-8 h-8 text-white"
              />
            </div>
            <h3 class="text-2xl font-bold text-slate-900 mb-4">全流程自动化</h3>
            <p class="text-slate-600 leading-relaxed">
              从数据录入到业务处理，从审批流程到结果输出，实现全流程自动化处理
            </p>
          </div>

          <!-- 多端协同 -->
          <div
            class="bg-white rounded-2xl p-8 hover:shadow-xl transition-all duration-300 group"
          >
            <div
              class="w-16 h-16 bg-primary-500 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300"
            >
              <UIcon
                name="i-heroicons-device-phone-mobile"
                class="w-8 h-8 text-white"
              />
            </div>
            <h3 class="text-2xl font-bold text-slate-900 mb-4">多端协同</h3>
            <p class="text-slate-600 leading-relaxed">
              支持PC端、移动端、微信小程序等多端协同，随时随地处理业务
            </p>
          </div>

          <!-- 智能决策 -->
          <div
            class="bg-white rounded-2xl p-8 hover:shadow-xl transition-all duration-300 group"
          >
            <div
              class="w-16 h-16 bg-primary-500 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300"
            >
              <UIcon name="i-heroicons-brain" class="w-8 h-8 text-white" />
            </div>
            <h3 class="text-2xl font-bold text-slate-900 mb-4">智能决策</h3>
            <p class="text-slate-600 leading-relaxed">
              AI驱动的智能决策引擎，自动分析业务数据，提供最优处理方案
            </p>
          </div>

          <!-- 高效处理 -->
          <div
            class="bg-white rounded-2xl p-8 hover:shadow-xl transition-all duration-300 group"
          >
            <div
              class="w-16 h-16 bg-primary-500 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300"
            >
              <UIcon name="i-heroicons-bolt" class="w-8 h-8 text-white" />
            </div>
            <h3 class="text-2xl font-bold text-slate-900 mb-4">高效处理</h3>
            <p class="text-slate-600 leading-relaxed">
              处理效率提升80%，大幅缩短业务处理时间，提升客户满意度
            </p>
          </div>

          <!-- 灵活配置 -->
          <div
            class="bg-white rounded-2xl p-8 hover:shadow-xl transition-all duration-300 group"
          >
            <div
              class="w-16 h-16 bg-primary-500 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300"
            >
              <UIcon
                name="i-heroicons-wrench-screwdriver"
                class="w-8 h-8 text-white"
              />
            </div>
            <h3 class="text-2xl font-bold text-slate-900 mb-4">灵活配置</h3>
            <p class="text-slate-600 leading-relaxed">
              可视化流程配置工具，支持个性化业务规则定制，快速适配不同需求
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- 核心功能 -->
    <section class="py-20 lg:py-28 bg-white">
      <div class="container mx-auto px-6">
        <div class="text-center mb-16">
          <h2 class="text-4xl lg:text-5xl font-bold text-slate-900 mb-6">
            核心功能介绍
          </h2>
          <p class="text-xl text-slate-600 max-w-4xl mx-auto font-light">
            四大核心功能模块，全方位提升您的业务处理效率
          </p>
        </div>

        <div class="space-y-20 max-w-7xl mx-auto">
          <!-- 可视化流程设计 -->
          <div class="flex flex-col lg:flex-row items-center gap-12">
            <div class="w-full lg:w-1/2">
              <img
                src="/img/photos/laptop-3196481_640.jpg"
                alt="可视化流程设计"
                class="w-full h-80 object-cover rounded-2xl shadow-lg"
              />
            </div>
            <div class="w-full lg:w-1/2">
              <div
                class="w-16 h-16 bg-primary-500 rounded-2xl flex items-center justify-center mb-6"
              >
                <UIcon
                  name="i-heroicons-squares-2x2"
                  class="w-8 h-8 text-white"
                />
              </div>
              <h3 class="text-3xl font-bold text-slate-900 mb-4">
                可视化流程设计
              </h3>
              <p class="text-lg text-slate-600 mb-6 leading-relaxed">
                拖拽式流程设计工具，支持复杂业务逻辑的可视化配置。内置丰富的业务组件库，快速构建自动化流程，无需编程知识即可完成流程设计。
              </p>
              <div class="space-y-3 mb-8">
                <div class="flex items-start space-x-3">
                  <div
                    class="w-2 h-2 bg-primary-500 rounded-full mt-2.5 flex-shrink-0"
                  ></div>
                  <span class="text-slate-600">拖拽式设计，操作简单直观</span>
                </div>
                <div class="flex items-start space-x-3">
                  <div
                    class="w-2 h-2 bg-primary-500 rounded-full mt-2.5 flex-shrink-0"
                  ></div>
                  <span class="text-slate-600">丰富的业务组件库</span>
                </div>
                <div class="flex items-start space-x-3">
                  <div
                    class="w-2 h-2 bg-primary-500 rounded-full mt-2.5 flex-shrink-0"
                  ></div>
                  <span class="text-slate-600">支持复杂业务逻辑配置</span>
                </div>
              </div>
              <UButton
                class="bg-primary-500 hover:bg-primary-600 text-white px-8 py-3"
                @click="openInquiryDrawer"
              >
                立即咨询
                <UIcon name="i-heroicons-arrow-right" class="w-4 h-4 ml-1" />
              </UButton>
            </div>
          </div>

          <!-- 智能数据处理 -->
          <div class="flex flex-col lg:flex-row-reverse items-center gap-12">
            <div class="w-full lg:w-1/2">
              <img
                src="/img/photos/office-620822_640.jpg"
                alt="智能数据处理"
                class="w-full h-80 object-cover rounded-2xl shadow-lg"
              />
            </div>
            <div class="w-full lg:w-1/2">
              <div
                class="w-16 h-16 bg-primary-500 rounded-2xl flex items-center justify-center mb-6"
              >
                <UIcon name="i-heroicons-cpu-chip" class="w-8 h-8 text-white" />
              </div>
              <h3 class="text-3xl font-bold text-slate-900 mb-4">
                智能数据处理
              </h3>
              <p class="text-lg text-slate-600 mb-6 leading-relaxed">
                AI驱动的数据处理引擎，自动识别、清洗、转换各类业务数据。支持多格式数据导入，智能数据验证，确保数据质量和处理准确性。
              </p>
              <div class="space-y-3 mb-8">
                <div class="flex items-start space-x-3">
                  <div
                    class="w-2 h-2 bg-primary-500 rounded-full mt-2.5 flex-shrink-0"
                  ></div>
                  <span class="text-slate-600">AI驱动的数据处理引擎</span>
                </div>
                <div class="flex items-start space-x-3">
                  <div
                    class="w-2 h-2 bg-primary-500 rounded-full mt-2.5 flex-shrink-0"
                  ></div>
                  <span class="text-slate-600">支持多格式数据导入</span>
                </div>
                <div class="flex items-start space-x-3">
                  <div
                    class="w-2 h-2 bg-primary-500 rounded-full mt-2.5 flex-shrink-0"
                  ></div>
                  <span class="text-slate-600">智能数据验证和质量控制</span>
                </div>
              </div>
              <UButton
                class="bg-primary-500 hover:bg-primary-600 text-white px-8 py-3"
                @click="openInquiryDrawer"
              >
                立即咨询
                <UIcon name="i-heroicons-arrow-right" class="w-4 h-4 ml-1" />
              </UButton>
            </div>
          </div>

          <!-- 多端协同处理 -->
          <div class="flex flex-col lg:flex-row items-center gap-12">
            <div class="w-full lg:w-1/2">
              <img
                src="/img/photos/meeting-2284501_640.jpg"
                alt="多端协同处理"
                class="w-full h-80 object-cover rounded-2xl shadow-lg"
              />
            </div>
            <div class="w-full lg:w-1/2">
              <div
                class="w-16 h-16 bg-primary-500 rounded-2xl flex items-center justify-center mb-6"
              >
                <UIcon
                  name="i-heroicons-device-phone-mobile"
                  class="w-8 h-8 text-white"
                />
              </div>
              <h3 class="text-3xl font-bold text-slate-900 mb-4">
                多端协同处理
              </h3>
              <p class="text-lg text-slate-600 mb-6 leading-relaxed">
                支持PC端、移动端、微信小程序等多端协同处理。统一的业务处理引擎确保各端数据同步，支持离线处理和数据同步，提升业务处理灵活性。
              </p>
              <div class="space-y-3 mb-8">
                <div class="flex items-start space-x-3">
                  <div
                    class="w-2 h-2 bg-primary-500 rounded-full mt-2.5 flex-shrink-0"
                  ></div>
                  <span class="text-slate-600">多端协同，数据实时同步</span>
                </div>
                <div class="flex items-start space-x-3">
                  <div
                    class="w-2 h-2 bg-primary-500 rounded-full mt-2.5 flex-shrink-0"
                  ></div>
                  <span class="text-slate-600">支持离线处理和数据同步</span>
                </div>
                <div class="flex items-start space-x-3">
                  <div
                    class="w-2 h-2 bg-primary-500 rounded-full mt-2.5 flex-shrink-0"
                  ></div>
                  <span class="text-slate-600">统一的业务处理引擎</span>
                </div>
              </div>
              <UButton
                class="bg-primary-500 hover:bg-primary-600 text-white px-8 py-3"
                @click="openInquiryDrawer"
              >
                立即咨询
                <UIcon name="i-heroicons-arrow-right" class="w-4 h-4 ml-1" />
              </UButton>
            </div>
          </div>

          <!-- 智能监控分析 -->
          <div class="flex flex-col lg:flex-row-reverse items-center gap-12">
            <div class="w-full lg:w-1/2">
              <img
                src="/img/photos/man-593333_640.jpg"
                alt="智能监控分析"
                class="w-full h-80 object-cover rounded-2xl shadow-lg"
              />
            </div>
            <div class="w-full lg:w-1/2">
              <div
                class="w-16 h-16 bg-primary-500 rounded-2xl flex items-center justify-center mb-6"
              >
                <UIcon
                  name="i-heroicons-chart-bar"
                  class="w-8 h-8 text-white"
                />
              </div>
              <h3 class="text-3xl font-bold text-slate-900 mb-4">
                智能监控分析
              </h3>
              <p class="text-lg text-slate-600 mb-6 leading-relaxed">
                实时监控业务流程执行情况，提供详细的性能分析和优化建议。可视化仪表盘展示关键指标，支持异常预警和自动处理，确保业务流程稳定运行。
              </p>
              <div class="space-y-3 mb-8">
                <div class="flex items-start space-x-3">
                  <div
                    class="w-2 h-2 bg-primary-500 rounded-full mt-2.5 flex-shrink-0"
                  ></div>
                  <span class="text-slate-600">实时监控业务流程执行</span>
                </div>
                <div class="flex items-start space-x-3">
                  <div
                    class="w-2 h-2 bg-primary-500 rounded-full mt-2.5 flex-shrink-0"
                  ></div>
                  <span class="text-slate-600">可视化仪表盘展示关键指标</span>
                </div>
                <div class="flex items-start space-x-3">
                  <div
                    class="w-2 h-2 bg-primary-500 rounded-full mt-2.5 flex-shrink-0"
                  ></div>
                  <span class="text-slate-600">异常预警和自动处理</span>
                </div>
              </div>
              <UButton
                class="bg-primary-500 hover:bg-primary-600 text-white px-8 py-3"
                @click="openInquiryDrawer"
              >
                立即咨询
                <UIcon name="i-heroicons-arrow-right" class="w-4 h-4 ml-1" />
              </UButton>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 应用场景 -->
    <section class="py-20 lg:py-28 bg-primary-50">
      <div class="container mx-auto px-6">
        <div class="text-center mb-16">
          <h2 class="text-4xl lg:text-5xl font-bold text-slate-900 mb-6">
            应用场景介绍
          </h2>
          <p class="text-xl text-slate-600 max-w-4xl mx-auto font-light">
            适用于多种业务场景，满足不同规模机构的自动化需求
          </p>
        </div>

        <div class="grid lg:grid-cols-2 gap-12 max-w-7xl mx-auto">
          <!-- 保险业务自动化 -->
          <div
            class="bg-white rounded-3xl p-8 border border-primary-200 hover:shadow-2xl transition-all duration-300 group"
          >
            <div class="flex items-start space-x-6">
              <div class="relative">
                <img
                  src="/img/photos/desk-3139127_640.jpg"
                  alt="保险业务自动化"
                  class="w-24 h-24 object-cover rounded-2xl"
                />
                <div
                  class="absolute -bottom-2 -right-2 w-10 h-10 bg-primary-500 rounded-xl flex items-center justify-center"
                >
                  <UIcon
                    name="i-heroicons-building-office"
                    class="w-5 h-5 text-white"
                  />
                </div>
              </div>
              <div class="flex-1">
                <h3
                  class="text-2xl font-bold text-slate-900 mb-3 group-hover:text-slate-600 transition-colors"
                >
                  保险业务自动化
                </h3>
                <p class="text-slate-600 mb-4 leading-relaxed">
                  自动化处理投保、核保、理赔等保险业务流程，大幅提升处理效率，降低人工成本，提升客户满意度。
                </p>
                <div class="space-y-2 mb-6">
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span><strong>效率提升：</strong>处理效率提升80%</span>
                  </div>
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span><strong>成本控制：</strong>降低人工成本60%</span>
                  </div>
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span><strong>客户体验：</strong>提升客户满意度</span>
                  </div>
                </div>
                <UButton
                  variant="outline"
                  color="neutral"
                  class="group-hover:bg-primary-500 group-hover:text-white group-hover:border-primary-500 hover:bg-slate-500 hover:text-white transition-all duration-300"
                  @click="openInquiryDrawer"
                >
                  立即咨询
                  <UIcon name="i-heroicons-arrow-right" class="w-4 h-4 ml-1" />
                </UButton>
              </div>
            </div>
          </div>

          <!-- 金融业务处理 -->
          <div
            class="bg-white rounded-3xl p-8 border border-primary-200 hover:shadow-2xl transition-all duration-300 group"
          >
            <div class="flex items-start space-x-6">
              <div class="relative">
                <img
                  src="/img/photos/job-5382501_640.jpg"
                  alt="金融业务处理"
                  class="w-24 h-24 object-cover rounded-2xl"
                />
                <div
                  class="absolute -bottom-2 -right-2 w-10 h-10 bg-primary-500 rounded-xl flex items-center justify-center"
                >
                  <UIcon
                    name="i-heroicons-credit-card"
                    class="w-5 h-5 text-white"
                  />
                </div>
              </div>
              <div class="flex-1">
                <h3
                  class="text-2xl font-bold text-slate-900 mb-3 group-hover:text-slate-600 transition-colors"
                >
                  金融业务处理
                </h3>
                <p class="text-slate-600 mb-4 leading-relaxed">
                  自动化处理贷款审批、风险评估、合规检查等金融业务流程，提高处理准确性，降低操作风险。
                </p>
                <div class="space-y-2 mb-6">
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span><strong>准确性：</strong>处理准确性提升90%</span>
                  </div>
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span><strong>风险控制：</strong>降低操作风险</span>
                  </div>
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span><strong>合规性：</strong>自动合规检查</span>
                  </div>
                </div>
                <UButton
                  variant="outline"
                  color="neutral"
                  class="group-hover:bg-primary-500 group-hover:text-white group-hover:border-primary-500 hover:bg-slate-500 hover:text-white transition-all duration-300"
                  @click="openInquiryDrawer"
                >
                  立即咨询
                  <UIcon name="i-heroicons-arrow-right" class="w-4 h-4 ml-1" />
                </UButton>
              </div>
            </div>
          </div>

          <!-- 企业流程优化 -->
          <div
            class="bg-white rounded-3xl p-8 border border-primary-200 hover:shadow-2xl transition-all duration-300 group"
          >
            <div class="flex items-start space-x-6">
              <div class="relative">
                <img
                  src="/img/photos/laptops-593296_640.jpg"
                  alt="企业流程优化"
                  class="w-24 h-24 object-cover rounded-2xl"
                />
                <div
                  class="absolute -bottom-2 -right-2 w-10 h-10 bg-primary-500 rounded-xl flex items-center justify-center"
                >
                  <UIcon
                    name="i-heroicons-building-office-2"
                    class="w-5 h-5 text-white"
                  />
                </div>
              </div>
              <div class="flex-1">
                <h3
                  class="text-2xl font-bold text-slate-900 mb-3 group-hover:text-slate-600 transition-colors"
                >
                  企业流程优化
                </h3>
                <p class="text-slate-600 mb-4 leading-relaxed">
                  优化企业内部审批、采购、人事等业务流程，提高工作效率，减少重复性工作，释放员工创造力。
                </p>
                <div class="space-y-2 mb-6">
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span><strong>效率提升：</strong>工作效率提升70%</span>
                  </div>
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span><strong>成本节约：</strong>减少重复性工作</span>
                  </div>
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span><strong>员工价值：</strong>释放员工创造力</span>
                  </div>
                </div>
                <UButton
                  variant="outline"
                  color="neutral"
                  class="group-hover:bg-primary-500 group-hover:text-white group-hover:border-primary-500 hover:bg-slate-500 hover:text-white transition-all duration-300"
                  @click="openInquiryDrawer"
                >
                  立即咨询
                  <UIcon name="i-heroicons-arrow-right" class="w-4 h-4 ml-1" />
                </UButton>
              </div>
            </div>
          </div>

          <!-- 政府服务优化 -->
          <div
            class="bg-white rounded-3xl p-8 border border-primary-200 hover:shadow-2xl transition-all duration-300 group"
          >
            <div class="flex items-start space-x-6">
              <div class="relative">
                <img
                  src="/img/photos/meeting-2284501_640.jpg"
                  alt="政府服务优化"
                  class="w-24 h-24 object-cover rounded-2xl"
                />
                <div
                  class="absolute -bottom-2 -right-2 w-10 h-10 bg-primary-500 rounded-xl flex items-center justify-center"
                >
                  <UIcon
                    name="i-heroicons-building-library"
                    class="w-5 h-5 text-white"
                  />
                </div>
              </div>
              <div class="flex-1">
                <h3
                  class="text-2xl font-bold text-slate-900 mb-3 group-hover:text-slate-600 transition-colors"
                >
                  政府服务优化
                </h3>
                <p class="text-slate-600 mb-4 leading-relaxed">
                  优化政府审批、证照办理、公共服务等业务流程，提高服务效率，提升群众满意度，推动数字政府建设。
                </p>
                <div class="space-y-2 mb-6">
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span><strong>服务效率：</strong>审批效率提升85%</span>
                  </div>
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span><strong>群众满意：</strong>提升群众满意度</span>
                  </div>
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span><strong>数字政府：</strong>推动数字政府建设</span>
                  </div>
                </div>
                <UButton
                  variant="outline"
                  color="neutral"
                  class="group-hover:bg-primary-500 group-hover:text-white group-hover:border-primary-500 hover:bg-slate-500 hover:text-white transition-all duration-300"
                  @click="openInquiryDrawer"
                >
                  立即咨询
                  <UIcon name="i-heroicons-arrow-right" class="w-4 h-4 ml-1" />
                </UButton>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 产品优势 -->
    <section
      class="py-20 lg:py-28 bg-primary-900 text-white relative overflow-hidden"
    >
      <div class="container mx-auto px-6 relative">
        <div class="text-center mb-16">
          <h2 class="text-4xl lg:text-5xl font-bold mb-6">
            为什么选择赢睿 <span class="text-primary-400">自动化</span> 服务平台
          </h2>
          <p class="text-xl text-primary-300 max-w-4xl mx-auto font-light">
            数据驱动的智能自动化，助力您实现业务数字化转型
          </p>
        </div>

        <div class="grid lg:grid-cols-4 md:grid-cols-2 gap-8 max-w-6xl mx-auto">
          <div
            class="text-center bg-primary-800/50 backdrop-blur rounded-2xl p-8 border border-primary-700"
          >
            <div class="text-4xl lg:text-5xl font-bold text-primary-400 mb-4">
              80%
            </div>
            <div class="text-primary-300 text-lg font-medium mb-2">
              效率提升
            </div>
            <div class="text-sm text-primary-400">业务处理效率</div>
          </div>

          <div
            class="text-center bg-primary-800/50 backdrop-blur rounded-2xl p-8 border border-primary-700"
          >
            <div class="text-4xl lg:text-5xl font-bold text-primary-400 mb-4">
              60%
            </div>
            <div class="text-primary-300 text-lg font-medium mb-2">
              成本降低
            </div>
            <div class="text-sm text-primary-400">人工成本节约</div>
          </div>

          <div
            class="text-center bg-primary-800/50 backdrop-blur rounded-2xl p-8 border border-primary-700"
          >
            <div class="text-4xl lg:text-5xl font-bold text-primary-400 mb-4">
              90%
            </div>
            <div class="text-primary-300 text-lg font-medium mb-2">准确率</div>
            <div class="text-sm text-primary-400">处理准确率</div>
          </div>

          <div
            class="text-center bg-primary-800/50 backdrop-blur rounded-2xl p-8 border border-primary-700"
          >
            <div class="text-4xl lg:text-5xl font-bold text-primary-400 mb-4">
              分钟级
            </div>
            <div class="text-primary-300 text-lg font-medium mb-2">
              快速部署
            </div>
            <div class="text-sm text-primary-400">即刻享受自动化</div>
          </div>
        </div>
      </div>
    </section>

    <!-- 咨询抽屉组件 -->
    <InquiryDrawer v-model="showInquiryDrawer" />
  </div>
</template>

<script setup lang="ts">
// SEO 配置
useHead({
  title: "多端自动化服务平台 - 赢睿保险科技",
  meta: [
    {
      name: "description",
      content:
        "业务流程自动化处理平台，支持多端协同，AI驱动的智能决策，提升处理效率80%，降低人工成本60%。",
    },
    {
      property: "og:title",
      content: "多端自动化服务平台 - 赢睿保险科技",
    },
    {
      property: "og:description",
      content: "业务流程自动化处理平台，支持多端协同，AI驱动的智能决策。",
    },
  ],
});

// 咨询抽屉状态
const showInquiryDrawer = ref(false);

// 打开咨询抽屉
const openInquiryDrawer = () => {
  showInquiryDrawer.value = true;
};

// 结构化数据
const structuredData = {
  "@context": "https://schema.org",
  "@type": "SoftwareApplication",
  name: "多端自动化服务平台",
  description: "业务流程自动化处理平台，支持多端协同，AI驱动的智能决策",
  applicationCategory: "BusinessApplication",
  operatingSystem: "Web",
  offers: {
    "@type": "Offer",
    price: "0",
    priceCurrency: "CNY",
  },
};

useHead({
  script: [
    {
      type: "application/ld+json",
      innerHTML: JSON.stringify(structuredData),
    },
  ],
});
</script>

<style scoped>
.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
