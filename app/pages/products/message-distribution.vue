<template>
  <div class="min-h-screen bg-white">
    <!-- Hero Section -->
    <section
      class="relative overflow-hidden bg-primary-900 pt-32 lg:pt-60 pb-20 lg:pb-28"
    >
      <div class="relative container mx-auto px-6">
        <div class="max-w-5xl mx-auto text-center text-white">
          <!-- 面包屑导航 -->
          <nav class="mb-8">
            <ol
              class="flex justify-center items-center space-x-2 text-sm text-primary-300"
            >
              <li>
                <NuxtLink
                  to="/"
                  class="hover:text-primary-400 transition-colors"
                >
                  首页
                </NuxtLink>
              </li>
              <li>
                <UIcon name="i-heroicons-chevron-right" class="w-4 h-4" />
              </li>
              <li>
                <NuxtLink
                  to="/products"
                  class="hover:text-primary-400 transition-colors"
                >
                  产品体系
                </NuxtLink>
              </li>
              <li>
                <UIcon name="i-heroicons-chevron-right" class="w-4 h-4" />
              </li>
              <li class="text-primary-400">全平台消息管理系统</li>
            </ol>
          </nav>

          <h1
            class="text-4xl lg:text-6xl font-bold mb-8 leading-tight animate-fade-in-up"
            style="animation-delay: 0.2s"
          >
            全平台消息管理系统
            <span
              class="block text-2xl lg:text-3xl font-light mt-4 text-primary-200"
            >
              保险行业智能消息中枢
            </span>
          </h1>
        </div>
      </div>
    </section>

    <!-- 产品特点 -->
    <section class="py-20 lg:py-28 bg-primary-50">
      <div class="container mx-auto px-6">
        <div class="text-center mb-16">
          <h2 class="text-4xl lg:text-5xl font-bold text-slate-900 mb-6">
            产品核心特点
          </h2>
          <p class="text-xl text-slate-600 max-w-4xl mx-auto font-light">
            专为财险公司打造的智能消息中枢，助力保险公司和中介机构实现数字化转型
          </p>
        </div>

        <div class="grid lg:grid-cols-3 md:grid-cols-2 gap-8 max-w-7xl mx-auto">
          <!-- 多渠道适配 -->
          <div
            class="bg-white rounded-2xl p-8 hover:shadow-xl transition-all duration-300 group"
          >
            <div
              class="w-16 h-16 bg-primary-500 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300"
            >
              <UIcon
                name="i-heroicons-squares-2x2"
                class="w-8 h-8 text-white"
              />
            </div>
            <h3 class="text-2xl font-bold text-slate-900 mb-4">多渠道适配</h3>
            <p class="text-slate-600 leading-relaxed">
              一键推送8大渠道，自动适配各渠道格式与要求
            </p>
          </div>

          <!-- 智能触达 -->
          <div
            class="bg-white rounded-2xl p-8 hover:shadow-xl transition-all duration-300 group"
          >
            <div
              class="w-16 h-16 bg-primary-500 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300"
            >
              <UIcon name="i-heroicons-bell-alert" class="w-8 h-8 text-white" />
            </div>
            <h3 class="text-2xl font-bold text-slate-900 mb-4">智能触达</h3>
            <p class="text-slate-600 leading-relaxed">
              匹配客户偏好方式，触达率高达98%
            </p>
          </div>

          <!-- 合规保障 -->
          <div
            class="bg-white rounded-2xl p-8 hover:shadow-xl transition-all duration-300 group"
          >
            <div
              class="w-16 h-16 bg-primary-500 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300"
            >
              <UIcon
                name="i-heroicons-shield-check"
                class="w-8 h-8 text-white"
              />
            </div>
            <h3 class="text-2xl font-bold text-slate-900 mb-4">合规保障</h3>
            <p class="text-slate-600 leading-relaxed">
              自动预审降低风险，拦截30%以上违规表述
            </p>
          </div>

          <!-- 高效部署 -->
          <div
            class="bg-white rounded-2xl p-8 hover:shadow-xl transition-all duration-300 group"
          >
            <div
              class="w-16 h-16 bg-primary-500 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300"
            >
              <UIcon name="i-heroicons-bolt" class="w-8 h-8 text-white" />
            </div>
            <h3 class="text-2xl font-bold text-slate-900 mb-4">高效部署</h3>
            <p class="text-slate-600 leading-relaxed">
              快速适配业务需求，分钟级上线
            </p>
          </div>

          <!-- 数据驱动 -->
          <div
            class="bg-white rounded-2xl p-8 hover:shadow-xl transition-all duration-300 group"
          >
            <div
              class="w-16 h-16 bg-primary-500 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300"
            >
              <UIcon name="i-heroicons-chart-bar" class="w-8 h-8 text-white" />
            </div>
            <h3 class="text-2xl font-bold text-slate-900 mb-4">数据驱动</h3>
            <p class="text-slate-600 leading-relaxed">
              实时分析推送效果，持续优化触达策略
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- 核心功能 -->
    <section class="py-20 lg:py-28 bg-white">
      <div class="container mx-auto px-6">
        <div class="text-center mb-16">
          <h2 class="text-4xl lg:text-5xl font-bold text-slate-900 mb-6">
            核心功能介绍
          </h2>
          <p class="text-xl text-slate-600 max-w-4xl mx-auto font-light">
            四大核心功能模块，全方位提升您的消息管理效率
          </p>
        </div>

        <div class="space-y-20 max-w-7xl mx-auto">
          <!-- 一键多渠道推送 -->
          <div class="flex flex-col lg:flex-row items-center gap-12">
            <div class="w-full lg:w-1/2">
              <img
                src="/img/photos/laptop-3196481_640.jpg"
                alt="一键多渠道推送"
                class="w-full h-80 object-cover rounded-2xl shadow-lg"
              />
            </div>
            <div class="w-full lg:w-1/2">
              <div
                class="w-16 h-16 bg-primary-500 rounded-2xl flex items-center justify-center mb-6"
              >
                <UIcon
                  name="i-heroicons-paper-airplane"
                  class="w-8 h-8 text-white"
                />
              </div>
              <h3 class="text-3xl font-bold text-slate-900 mb-4">
                一键多渠道推送
              </h3>
              <p class="text-lg text-slate-600 mb-6 leading-relaxed">
                赢睿科技全平台消息管理系统支持短信、APP、微信等8大渠道同步推送，业务员只需一次编辑内容，系统自动适配各渠道格式与要求，省去繁琐的重复操作。针对年轻车主优先推送微信，中老年客户偏好短信，智能匹配确保信息精准触达，触达率高达98%。相比传统逐渠道人工发送，效率提升10倍以上，续保提醒等关键消息从3小时缩短至10分钟完成，显著降低运营成本，提升客户满意度。
              </p>
              <div class="space-y-3 mb-8">
                <div class="flex items-start space-x-3">
                  <div
                    class="w-2 h-2 bg-primary-500 rounded-full mt-2.5 flex-shrink-0"
                  ></div>
                  <span class="text-slate-600">触达率高达98%</span>
                </div>
                <div class="flex items-start space-x-3">
                  <div
                    class="w-2 h-2 bg-primary-500 rounded-full mt-2.5 flex-shrink-0"
                  ></div>
                  <span class="text-slate-600">效率提升10倍以上</span>
                </div>
                <div class="flex items-start space-x-3">
                  <div
                    class="w-2 h-2 bg-primary-500 rounded-full mt-2.5 flex-shrink-0"
                  ></div>
                  <span class="text-slate-600"
                    >关键消息从3小时缩短至10分钟完成</span
                  >
                </div>
              </div>
              <UButton
                class="bg-primary-500 hover:bg-primary-600 text-white px-8 py-3"
                @click="openInquiryDrawer"
              >
                立即咨询
                <UIcon name="i-heroicons-arrow-right" class="w-4 h-4 ml-1" />
              </UButton>
            </div>
          </div>

          <!-- 智能合规预审 -->
          <div class="flex flex-col lg:flex-row-reverse items-center gap-12">
            <div class="w-full lg:w-1/2">
              <img
                src="/img/photos/office-620822_640.jpg"
                alt="智能合规预审"
                class="w-full h-80 object-cover rounded-2xl shadow-lg"
              />
            </div>
            <div class="w-full lg:w-1/2">
              <div
                class="w-16 h-16 bg-primary-500 rounded-2xl flex items-center justify-center mb-6"
              >
                <UIcon
                  name="i-heroicons-shield-check"
                  class="w-8 h-8 text-white"
                />
              </div>
              <h3 class="text-3xl font-bold text-slate-900 mb-4">
                智能合规预审
              </h3>
              <p class="text-lg text-slate-600 mb-6 leading-relaxed">
                系统内置监管话术库与自动预审功能，实时扫描推送内容，拦截30%以上违规表述风险，助力保险公司规避合规罚单。无论是续保通知还是理赔提醒，赢睿科技确保每条消息符合监管要求，减少人工审核负担。预审过程仅需秒级完成，兼顾效率与安全。客户可自定义话术模板，进一步适配业务场景，合规管理从未如此简单！
              </p>
              <div class="space-y-3 mb-8">
                <div class="flex items-start space-x-3">
                  <div
                    class="w-2 h-2 bg-primary-500 rounded-full mt-2.5 flex-shrink-0"
                  ></div>
                  <span class="text-slate-600">拦截30%以上违规表述风险</span>
                </div>
                <div class="flex items-start space-x-3">
                  <div
                    class="w-2 h-2 bg-primary-500 rounded-full mt-2.5 flex-shrink-0"
                  ></div>
                  <span class="text-slate-600">预审过程仅需秒级完成</span>
                </div>
                <div class="flex items-start space-x-3">
                  <div
                    class="w-2 h-2 bg-primary-500 rounded-full mt-2.5 flex-shrink-0"
                  ></div>
                  <span class="text-slate-600">支持自定义话术模板</span>
                </div>
              </div>
              <UButton
                class="bg-primary-500 hover:bg-primary-600 text-white px-8 py-3"
                @click="openInquiryDrawer"
              >
                立即咨询
                <UIcon name="i-heroicons-arrow-right" class="w-4 h-4 ml-1" />
              </UButton>
            </div>
          </div>

          <!-- 个性化客户触达 -->
          <div class="flex flex-col lg:flex-row items-center gap-12">
            <div class="w-full lg:w-1/2">
              <img
                src="/img/photos/meeting-2284501_640.jpg"
                alt="个性化客户触达"
                class="w-full h-80 object-cover rounded-2xl shadow-lg"
              />
            </div>
            <div class="w-full lg:w-1/2">
              <div
                class="w-16 h-16 bg-primary-500 rounded-2xl flex items-center justify-center mb-6"
              >
                <UIcon
                  name="i-heroicons-user-circle"
                  class="w-8 h-8 text-white"
                />
              </div>
              <h3 class="text-3xl font-bold text-slate-900 mb-4">
                个性化客户触达
              </h3>
              <p class="text-lg text-slate-600 mb-6 leading-relaxed">
                通过智能算法分析客户画像与行为偏好，系统自动选择最佳推送渠道与时间。例如，年轻车主更倾向于夜间接收微信通知，而老年客户偏好上午短信提醒。赢睿科技助力保险公司实现千人千面的精准沟通，提升客户体验，降低40%因信息未达引发的投诉率。续保成功率因此提升15%，为业务增长注入新动力。
              </p>
              <div class="space-y-3 mb-8">
                <div class="flex items-start space-x-3">
                  <div
                    class="w-2 h-2 bg-primary-500 rounded-full mt-2.5 flex-shrink-0"
                  ></div>
                  <span class="text-slate-600"
                    >降低40%因信息未达引发的投诉率</span
                  >
                </div>
                <div class="flex items-start space-x-3">
                  <div
                    class="w-2 h-2 bg-primary-500 rounded-full mt-2.5 flex-shrink-0"
                  ></div>
                  <span class="text-slate-600">续保成功率提升15%</span>
                </div>
                <div class="flex items-start space-x-3">
                  <div
                    class="w-2 h-2 bg-primary-500 rounded-full mt-2.5 flex-shrink-0"
                  ></div>
                  <span class="text-slate-600">实现千人千面的精准沟通</span>
                </div>
              </div>
              <UButton
                class="bg-primary-500 hover:bg-primary-600 text-white px-8 py-3"
                @click="openInquiryDrawer"
              >
                立即咨询
                <UIcon name="i-heroicons-arrow-right" class="w-4 h-4 ml-1" />
              </UButton>
            </div>
          </div>

          <!-- 自动化场景触发 -->
          <div class="flex flex-col lg:flex-row-reverse items-center gap-12">
            <div class="w-full lg:w-1/2">
              <img
                src="/img/photos/man-593333_640.jpg"
                alt="自动化场景触发"
                class="w-full h-80 object-cover rounded-2xl shadow-lg"
              />
            </div>
            <div class="w-full lg:w-1/2">
              <div
                class="w-16 h-16 bg-primary-500 rounded-2xl flex items-center justify-center mb-6"
              >
                <UIcon name="i-heroicons-bolt" class="w-8 h-8 text-white" />
              </div>
              <h3 class="text-3xl font-bold text-slate-900 mb-4">
                自动化场景触发
              </h3>
              <p class="text-lg text-slate-600 mb-6 leading-relaxed">
                支持理赔进度更新、灾害预警、续保提醒等场景的自动触发推送，无需人工干预。例如，暴雨预警可根据气象数据即时推送至受影响区域客户，10分钟内完成数万条消息分发，效率远超传统人工通知。赢睿科技的自动化机制将重复性工作量减少80%，让业务员聚焦高价值任务，助力机构降本增效。
              </p>
              <div class="space-y-3 mb-8">
                <div class="flex items-start space-x-3">
                  <div
                    class="w-2 h-2 bg-primary-500 rounded-full mt-2.5 flex-shrink-0"
                  ></div>
                  <span class="text-slate-600">10分钟内完成数万条消息分发</span>
                </div>
                <div class="flex items-start space-x-3">
                  <div
                    class="w-2 h-2 bg-primary-500 rounded-full mt-2.5 flex-shrink-0"
                  ></div>
                  <span class="text-slate-600">重复性工作量减少80%</span>
                </div>
                <div class="flex items-start space-x-3">
                  <div
                    class="w-2 h-2 bg-primary-500 rounded-full mt-2.5 flex-shrink-0"
                  ></div>
                  <span class="text-slate-600">无需人工干预，自动触发推送</span>
                </div>
              </div>
              <UButton
                class="bg-primary-500 hover:bg-primary-600 text-white px-8 py-3"
                @click="openInquiryDrawer"
              >
                立即咨询
                <UIcon name="i-heroicons-arrow-right" class="w-4 h-4 ml-1" />
              </UButton>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 应用场景 -->
    <section class="py-20 lg:py-28 bg-primary-50">
      <div class="container mx-auto px-6">
        <div class="text-center mb-16">
          <h2 class="text-4xl lg:text-5xl font-bold text-slate-900 mb-6">
            应用场景介绍
          </h2>
          <p class="text-xl text-slate-600 max-w-4xl mx-auto font-light">
            适用于多种业务场景，满足不同规模机构的个性化需求
          </p>
        </div>

        <div class="grid lg:grid-cols-2 gap-12 max-w-7xl mx-auto">
          <!-- 车险续保提醒 -->
          <div
            class="bg-white rounded-3xl p-8 border border-primary-200 hover:shadow-2xl transition-all duration-300 group"
          >
            <div class="flex items-start space-x-6">
              <div class="relative">
                <img
                  src="/img/photos/desk-3139127_640.jpg"
                  alt="车险续保提醒"
                  class="w-24 h-24 object-cover rounded-2xl"
                />
                <div
                  class="absolute -bottom-2 -right-2 w-10 h-10 bg-primary-500 rounded-xl flex items-center justify-center"
                >
                  <UIcon name="i-heroicons-truck" class="w-5 h-5 text-white" />
                </div>
              </div>
              <div class="flex-1">
                <h3
                  class="text-2xl font-bold text-slate-900 mb-3 group-hover:text-slate-600 transition-colors"
                >
                  车险续保提醒
                </h3>
                <p class="text-slate-600 mb-4 leading-relaxed">
                  中小保险公司常因续保通知触达不及时导致客户流失。赢睿科技全平台消息管理系统通过一键推送续保提醒，自动适配客户偏好渠道，触达率达98%。智能算法分析客户续保意愿，优先推送高意向客户，续保成功率提升15%。从人工处理3小时到10分钟自动完成，效率大幅提升，客户投诉率下降40%。
                </p>
                <div class="space-y-2 mb-6">
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span><strong>触达率：</strong>高达98%</span>
                  </div>
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span><strong>续保率：</strong>提升15%</span>
                  </div>
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span><strong>效率提升：</strong>从3小时到10分钟</span>
                  </div>
                </div>
                <UButton
                  variant="outline"
                  color="neutral"
                  class="group-hover:bg-primary-500 group-hover:text-white group-hover:border-primary-500 hover:bg-slate-500 hover:text-white transition-all duration-300"
                  @click="openInquiryDrawer"
                >
                  立即咨询
                  <UIcon name="i-heroicons-arrow-right" class="w-4 h-4 ml-1" />
                </UButton>
              </div>
            </div>
          </div>

          <!-- 暴雨灾害预警 -->
          <div
            class="bg-white rounded-3xl p-8 border border-primary-200 hover:shadow-2xl transition-all duration-300 group"
          >
            <div class="flex items-start space-x-6">
              <div class="relative">
                <img
                  src="/img/photos/job-5382501_640.jpg"
                  alt="暴雨灾害预警"
                  class="w-24 h-24 object-cover rounded-2xl"
                />
                <div
                  class="absolute -bottom-2 -right-2 w-10 h-10 bg-primary-500 rounded-xl flex items-center justify-center"
                >
                  <UIcon name="i-heroicons-cloud" class="w-5 h-5 text-white" />
                </div>
              </div>
              <div class="flex-1">
                <h3
                  class="text-2xl font-bold text-slate-900 mb-3 group-hover:text-slate-600 transition-colors"
                >
                  暴雨灾害预警
                </h3>
                <p class="text-slate-600 mb-4 leading-relaxed">
                  暴雨等灾害发生时，保险公司需快速通知客户防范风险。赢睿科技系统支持基于气象数据的自动触发推送，10分钟内将预警信息送达数万客户，覆盖短信、微信等渠道。精准分发避免信息冗余，客户满意度显著提升。相比传统人工通知，效率提升数十倍，助力机构树立专业形象。
                </p>
                <div class="space-y-2 mb-6">
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span
                      ><strong>响应速度：</strong>10分钟内完成数万条推送</span
                    >
                  </div>
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span><strong>多渠道覆盖：</strong>短信、微信等全覆盖</span>
                  </div>
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span
                      ><strong>效率提升：</strong>比传统人工通知提升数十倍</span
                    >
                  </div>
                </div>
                <UButton
                  variant="outline"
                  color="neutral"
                  class="group-hover:bg-primary-500 group-hover:text-white group-hover:border-primary-500 hover:bg-slate-500 hover:text-white transition-all duration-300"
                  @click="openInquiryDrawer"
                >
                  立即咨询
                  <UIcon name="i-heroicons-arrow-right" class="w-4 h-4 ml-1" />
                </UButton>
              </div>
            </div>
          </div>

          <!-- 理赔进度通知 -->
          <div
            class="bg-white rounded-3xl p-8 border border-primary-200 hover:shadow-2xl transition-all duration-300 group"
          >
            <div class="flex items-start space-x-6">
              <div class="relative">
                <img
                  src="/img/photos/laptops-593296_640.jpg"
                  alt="理赔进度通知"
                  class="w-24 h-24 object-cover rounded-2xl"
                />
                <div
                  class="absolute -bottom-2 -right-2 w-10 h-10 bg-primary-500 rounded-xl flex items-center justify-center"
                >
                  <UIcon
                    name="i-heroicons-clipboard-document-check"
                    class="w-5 h-5 text-white"
                  />
                </div>
              </div>
              <div class="flex-1">
                <h3
                  class="text-2xl font-bold text-slate-900 mb-3 group-hover:text-slate-600 transition-colors"
                >
                  理赔进度通知
                </h3>
                <p class="text-slate-600 mb-4 leading-relaxed">
                  客户对理赔进度缺乏透明信息常引发不满。赢睿科技系统自动触发理赔进度更新，实时推送至客户偏好渠道，如APP或短信，客户可随时掌握最新状态。自动化推送减少80%人工沟通成本，客户投诉率降低40%。中小保险公司借此提升服务质量，增强客户信任。
                </p>
                <div class="space-y-2 mb-6">
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span><strong>人工成本：</strong>减少80%沟通成本</span>
                  </div>
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span><strong>客户满意：</strong>投诉率降低40%</span>
                  </div>
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span><strong>实时更新：</strong>客户随时掌握最新状态</span>
                  </div>
                </div>
                <UButton
                  variant="outline"
                  color="neutral"
                  class="group-hover:bg-primary-500 group-hover:text-white group-hover:border-primary-500 hover:bg-slate-500 hover:text-white transition-all duration-300"
                  @click="openInquiryDrawer"
                >
                  立即咨询
                  <UIcon name="i-heroicons-arrow-right" class="w-4 h-4 ml-1" />
                </UButton>
              </div>
            </div>
          </div>

          <!-- 中介机构批量营销 -->
          <div
            class="bg-white rounded-3xl p-8 border border-primary-200 hover:shadow-2xl transition-all duration-300 group"
          >
            <div class="flex items-start space-x-6">
              <div class="relative">
                <img
                  src="/img/photos/meeting-2284501_640.jpg"
                  alt="中介机构批量营销"
                  class="w-24 h-24 object-cover rounded-2xl"
                />
                <div
                  class="absolute -bottom-2 -right-2 w-10 h-10 bg-primary-500 rounded-xl flex items-center justify-center"
                >
                  <UIcon
                    name="i-heroicons-megaphone"
                    class="w-5 h-5 text-white"
                  />
                </div>
              </div>
              <div class="flex-1">
                <h3
                  class="text-2xl font-bold text-slate-900 mb-3 group-hover:text-slate-600 transition-colors"
                >
                  中介机构批量营销
                </h3>
                <p class="text-slate-600 mb-4 leading-relaxed">
                  保险中介机构需高效触达潜在客户开展营销。赢睿科技系统支持批量推送定制化营销信息，智能匹配客户偏好渠道，触达率高达98%。内置合规预审确保内容合法，降低30%违规风险。营销活动从策划到执行仅需数小时，助力中介机构快速拓展业务。
                </p>
                <div class="space-y-2 mb-6">
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span><strong>触达率：</strong>高达98%</span>
                  </div>
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span><strong>合规保障：</strong>降低30%违规风险</span>
                  </div>
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span
                      ><strong>执行效率：</strong>从策划到执行仅需数小时</span
                    >
                  </div>
                </div>
                <UButton
                  variant="outline"
                  color="neutral"
                  class="group-hover:bg-primary-500 group-hover:text-white group-hover:border-primary-500 hover:bg-slate-500 hover:text-white transition-all duration-300"
                  @click="openInquiryDrawer"
                >
                  立即咨询
                  <UIcon name="i-heroicons-arrow-right" class="w-4 h-4 ml-1" />
                </UButton>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- 咨询抽屉组件 -->
    <InquiryDrawer v-model="showInquiryDrawer" />
  </div>
</template>

<script setup lang="ts">
// 页面元数据
useHead({
  title: "全平台消息管理系统 - 保险行业智能消息中枢 - 赢睿保险科技",
  meta: [
    {
      name: "description",
      content:
        "赢睿科技全平台消息管理系统，专为财险公司打造的智能消息中枢。助力保险公司和中介机构实现数字化转型。",
    },
    {
      name: "keywords",
      content:
        "消息管理系统,全平台消息,保险消息,智能消息中枢,保险科技,InsurTech",
    },
  ],
});

// 咨询抽屉状态
const showInquiryDrawer = ref(false);

// 打开咨询抽屉
const openInquiryDrawer = () => {
  showInquiryDrawer.value = true;
};
</script>
