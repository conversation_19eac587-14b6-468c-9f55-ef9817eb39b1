<template>
  <div class="min-h-screen bg-white">
    <!-- Hero Section -->
    <section class="relative overflow-hidden pt-32 lg:pt-60 pb-20 lg:pb-28">
      <!-- 背景图像 -->
      <div class="absolute inset-0">
        <img
          src="/img/photos/herobg4.jpg"
          alt="服务体系背景"
          class="w-full h-full object-cover"
        />
        <div class="absolute inset-0 bg-primary-900/80"></div>
      </div>

      <div class="relative container mx-auto px-6">
        <div class="max-w-4xl mx-auto text-center text-white">
          <h1
            class="text-4xl lg:text-6xl font-bold mb-8 leading-tight animate-fade-in-up"
            style="animation-delay: 0.2s"
          >
            <span class="text-primary-500">专业</span> 服务体系
            <span
              class="block text-2xl lg:text-3xl font-light mt-4 text-primary-200"
            >
              全方位保险科技服务解决方案
            </span>
          </h1>
        </div>
      </div>
    </section>

    <!-- 服务导航 -->
    <section
      class="py-6 bg-primary-50 top-24 z-40 border-b border-primary-200 sticky"
    >
      <div class="container mx-auto px-6">
        <div class="flex justify-center">
          <nav class="flex space-x-2 bg-white rounded-2xl p-2 shadow-xl/5">
            <button
              v-for="category in serviceCategories"
              :key="category.id"
              class="px-6 py-3 text-sm font-light rounded-xl transition-all duration-300"
              :class="[
                activeCategory === category.id
                  ? 'bg-primary-500 font-semibold text-white shadow-lg'
                  : 'text-slate-600 hover:text-slate-600 hover:bg-primary-50',
              ]"
              @click="scrollToCategory(category.id)"
            >
              {{ category.name }}
            </button>
          </nav>
        </div>
      </div>
    </section>

    <!-- 咨询服务 -->
    <section id="consulting-services" class="py-20 lg:py-28 bg-white">
      <div class="container mx-auto px-6">
        <div class="text-center mb-16">
          <h2 class="text-4xl lg:text-5xl font-bold text-slate-900 mb-6">
            <span class="text-primary-500">专业</span> 咨询服务
          </h2>
          <p class="text-xl text-slate-600 max-w-4xl mx-auto font-light">
            基于深厚行业经验与前沿技术，为保险机构提供全方位的专业咨询服务
          </p>
        </div>

        <div class="grid lg:grid-cols-2 gap-12 max-w-7xl mx-auto">
          <!-- 保险机构数智化转型咨询 -->
          <div
            class="bg-white rounded-3xl p-8 border border-primary-200 hover:shadow-2xl transition-all duration-300 group"
          >
            <div class="flex items-start space-x-6">
              <div class="relative">
                <img
                  src="/img/photos/meeting-2284501_640.jpg"
                  alt="数智化转型咨询"
                  class="w-24 h-24 object-cover rounded-2xl"
                />
                <div
                  class="absolute -bottom-2 -right-2 w-10 h-10 bg-primary-500 rounded-xl flex items-center justify-center"
                >
                  <UIcon
                    name="i-heroicons-light-bulb"
                    class="w-5 h-5 text-white"
                  />
                </div>
              </div>
              <div class="flex-1">
                <h3
                  class="text-2xl font-bold text-slate-900 mb-3 group-hover:text-slate-600 transition-colors"
                >
                  保险机构数智化转型咨询
                </h3>
                <p class="text-slate-600 mb-4 leading-relaxed font-light">
                  凭借前沿技术与深厚行业洞察，助力保险公司及中介机构突破数字化瓶颈，推动产品创新、流程优化及生态融合。
                </p>
                <div class="space-y-2 mb-6">
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span
                      ><strong>核心服务：</strong
                      >数据驱动洞察、智能承保理赔、开放生态平台</span
                    >
                  </div>
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span
                      ><strong>服务价值：</strong
                      >客户体验升级、运营效率提升、业务创新驱动</span
                    >
                  </div>
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span
                      ><strong>实施周期：</strong
                      >缩短20%，快速上线，成本更优</span
                    >
                  </div>
                </div>
                <NuxtLink to="/services/digital-transformation">
                  <UButton
                    variant="outline"
                    color="neutral"
                    class="group-hover:bg-primary-500 group-hover:text-white transition-all duration-300"
                  >
                    了解详情 →
                  </UButton>
                </NuxtLink>
              </div>
            </div>
          </div>

          <!-- IT等保合规咨询 -->
          <div
            class="bg-white rounded-3xl p-8 border border-primary-200 hover:shadow-2xl transition-all duration-300 group"
          >
            <div class="flex items-start space-x-6">
              <div class="relative">
                <img
                  src="/img/photos/office-620822_640.jpg"
                  alt="IT等保合规咨询"
                  class="w-24 h-24 object-cover rounded-2xl"
                />
                <div
                  class="absolute -bottom-2 -right-2 w-10 h-10 bg-primary-500 rounded-xl flex items-center justify-center"
                >
                  <UIcon
                    name="i-heroicons-shield-check"
                    class="w-5 h-5 text-white"
                  />
                </div>
              </div>
              <div class="flex-1">
                <h3
                  class="text-2xl font-bold text-slate-900 mb-3 group-hover:text-slate-600 transition-colors"
                >
                  IT等保合规咨询
                </h3>
                <p class="text-slate-600 mb-4 leading-relaxed font-light">
                  助力保险公司及中介机构轻松应对等保2.0合规挑战，提供从定级到测评的全生命周期解决方案。
                </p>
                <div class="space-y-2 mb-6">
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span
                      ><strong>核心服务：</strong
                      >合规定级备案、差距分析评估、安全整改方案</span
                    >
                  </div>
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span
                      ><strong>服务价值：</strong
                      >合规风险防控、网络安全提升、客户信任增强</span
                    >
                  </div>
                  <div
                    class="flex items-center space-x-2 text-sm text-slate-600"
                  >
                    <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <span
                      ><strong>整改效率：</strong
                      >快速弥合差距，缩短合规周期30%</span
                    >
                  </div>
                </div>
                <NuxtLink to="/services/it-compliance">
                  <UButton
                    variant="outline"
                    color="neutral"
                    class="group-hover:bg-primary-500 group-hover:text-white transition-all duration-300"
                  >
                    了解详情 →
                  </UButton>
                </NuxtLink>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 运营服务 -->
    <section id="operation-services" class="py-20 lg:py-28 bg-primary-50">
      <div class="container mx-auto px-6">
        <div class="text-center mb-16">
          <h2 class="text-4xl lg:text-5xl font-bold text-slate-900 mb-6">
            专业运营服务
          </h2>
          <p class="text-xl text-slate-600 max-w-4xl mx-auto font-light">
            覆盖货运保险、财产险中介等细分领域，提供全流程运营支持
          </p>
        </div>

        <div class="grid lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
          <!-- 货运保险MGA运营 -->
          <div
            class="bg-white rounded-2xl p-8 hover:shadow-xl transition-all duration-300 group"
          >
            <div
              class="w-16 h-16 bg-primary-500 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300"
            >
              <UIcon name="i-heroicons-truck" class="w-8 h-8 text-white" />
            </div>
            <h3 class="text-xl font-bold text-slate-900 mb-4">
              货运保险MGA运营
            </h3>
            <p class="text-slate-600 mb-6 leading-relaxed">
              通过定制化产品设计、全国服务网络及大数据风控，助力保险公司快速抢占货运险市场，显著降低15%运营成本。
            </p>
            <div class="space-y-2 text-sm text-slate-600 mb-6">
              <div class="flex items-center space-x-2">
                <div class="w-1.5 h-1.5 bg-primary-400 rounded-full"></div>
                <span>全国300+城市服务网络覆盖</span>
              </div>
              <div class="flex items-center space-x-2">
                <div class="w-1.5 h-1.5 bg-primary-400 rounded-full"></div>
                <span>新业务上线周期缩短至7个工作日</span>
              </div>
              <div class="flex items-center space-x-2">
                <div class="w-1.5 h-1.5 bg-primary-400 rounded-full"></div>
                <span>大数据驱动精准风控</span>
              </div>
            </div>
            <NuxtLink to="/services/mga-operation">
              <UButton
                variant="ghost"
                color="neutral"
                class="w-full group-hover:bg-primary-50"
              >
                了解详情 →
              </UButton>
            </NuxtLink>
          </div>

          <!-- 财产险中介机构运营 -->
          <div
            class="bg-white rounded-2xl p-8 hover:shadow-xl transition-all duration-300 group"
          >
            <div
              class="w-16 h-16 bg-primary-500 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300"
            >
              <UIcon
                name="i-heroicons-building-storefront"
                class="w-8 h-8 text-white"
              />
            </div>
            <h3 class="text-xl font-bold text-slate-900 mb-4">
              财产险中介机构运营
            </h3>
            <p class="text-slate-600 mb-6 leading-relaxed">
              依托全国500+合作网点及专业团队，覆盖企财险、工程险等全品类，年促成保费超30亿元，降低25%运营成本。
            </p>
            <div class="space-y-2 text-sm text-slate-600 mb-6">
              <div class="flex items-center space-x-2">
                <div class="w-1.5 h-1.5 bg-primary-400 rounded-full"></div>
                <span>90%标准业务3分钟即时报价</span>
              </div>
              <div class="flex items-center space-x-2">
                <div class="w-1.5 h-1.5 bg-primary-400 rounded-full"></div>
                <span>年均保费增长15%</span>
              </div>
              <div class="flex items-center space-x-2">
                <div class="w-1.5 h-1.5 bg-primary-400 rounded-full"></div>
                <span>线上化理赔率达85%</span>
              </div>
            </div>
            <NuxtLink to="/services/agency-operation">
              <UButton
                variant="ghost"
                color="neutral"
                class="w-full group-hover:bg-primary-50"
              >
                了解详情 →
              </UButton>
            </NuxtLink>
          </div>

          <!-- 保险社交媒体营销运营 -->
          <div
            class="bg-white rounded-2xl p-8 hover:shadow-xl transition-all duration-300 group"
          >
            <div
              class="w-16 h-16 bg-primary-500 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300"
            >
              <UIcon name="i-heroicons-megaphone" class="w-8 h-8 text-white" />
            </div>
            <h3 class="text-xl font-bold text-slate-900 mb-4">
              保险社交媒体营销运营
            </h3>
            <p class="text-slate-600 mb-6 leading-relaxed">
              依托微信、抖音、小红书等平台，整合内容创作、KOL合作及精准投放，获客成本降低40%，单条爆款内容提升咨询量300%。
            </p>
            <div class="space-y-2 text-sm text-slate-600 mb-6">
              <div class="flex items-center space-x-2">
                <div class="w-1.5 h-1.5 bg-primary-400 rounded-full"></div>
                <span>锁定Z世代企业主精准获客</span>
              </div>
              <div class="flex items-center space-x-2">
                <div class="w-1.5 h-1.5 bg-primary-400 rounded-full"></div>
                <span>粉丝增长150%，品牌影响力激增</span>
              </div>
              <div class="flex items-center space-x-2">
                <div class="w-1.5 h-1.5 bg-primary-400 rounded-full"></div>
                <span>全链路管理从内容到线索分配</span>
              </div>
            </div>
            <NuxtLink to="/services/social-media-marketing">
              <UButton
                variant="ghost"
                color="neutral"
                class="w-full group-hover:bg-primary-50"
              >
                了解详情 →
              </UButton>
            </NuxtLink>
          </div>
        </div>
      </div>
    </section>

    <!-- 法律服务 -->
    <section id="legal-services" class="py-20 lg:py-28 bg-white">
      <div class="container mx-auto px-6">
        <div class="text-center mb-16">
          <h2 class="text-4xl lg:text-5xl font-bold text-slate-900 mb-6">
            专业法律服务
          </h2>
          <p class="text-xl text-slate-600 max-w-4xl mx-auto font-light">
            结合最新法规与丰富案例库，为货运保险业务提供全流程法律支持
          </p>
        </div>

        <!-- 2列布局 -->
        <div class="max-w-6xl mx-auto">
          <div class="grid lg:grid-cols-2 gap-8">
            <!-- 货运保险法律咨询 -->
            <div
              class="bg-white rounded-2xl overflow-hidden group hover:shadow-xl transition-all duration-300"
            >
              <div class="relative">
                <img
                  src="/img/photos/laptop-3196481_640.jpg"
                  alt="货运保险法律咨询"
                  class="w-full h-64 lg:h-80 object-cover group-hover:scale-105 transition-transform duration-500"
                />
                <div class="absolute top-4 left-4">
                  <div
                    class="w-12 h-12 bg-primary-500 rounded-xl flex items-center justify-center"
                  >
                    <UIcon
                      name="i-heroicons-scale"
                      class="w-6 h-6 text-white"
                    />
                  </div>
                </div>
              </div>
              <div class="p-8">
                <h3
                  class="text-2xl lg:text-3xl font-bold text-slate-900 mb-4 group-hover:text-slate-600 transition-colors"
                >
                  货运保险法律咨询
                </h3>
                <p class="text-slate-600 mb-6 leading-relaxed">
                  结合《保险法》《民法典》及最新法规，提供全流程法律支持，覆盖承保、理赔及纠纷处理，助力企业降低合规风险、优化理赔效率。
                </p>
                <div class="grid md:grid-cols-2 gap-4 mb-6">
                  <div class="flex items-center space-x-3">
                    <div
                      class="w-2 h-2 bg-primary-500 rounded-full flex-shrink-0"
                    ></div>
                    <span class="text-sm text-slate-600">运输合同合规审查</span>
                  </div>
                  <div class="flex items-center space-x-3">
                    <div
                      class="w-2 h-2 bg-primary-500 rounded-full flex-shrink-0"
                    ></div>
                    <span class="text-sm text-slate-600">承运人责任界定</span>
                  </div>
                  <div class="flex items-center space-x-3">
                    <div
                      class="w-2 h-2 bg-primary-500 rounded-full flex-shrink-0"
                    ></div>
                    <span class="text-sm text-slate-600">代位求偿操作指引</span>
                  </div>
                  <div class="flex items-center space-x-3">
                    <div
                      class="w-2 h-2 bg-primary-500 rounded-full flex-shrink-0"
                    ></div>
                    <span class="text-sm text-slate-600">诉讼证据链构建</span>
                  </div>
                </div>
                <NuxtLink to="/services/legal-consulting">
                  <UButton
                    class="bg-primary-500 hover:bg-primary-600 text-white px-8 py-3"
                    to="/services/legal-consulting"
                  >
                    了解详情
                    <UIcon
                      name="i-heroicons-arrow-right"
                      class="w-4 h-4 ml-1"
                    />
                  </UButton>
                </NuxtLink>
              </div>
            </div>

            <!-- 服务优势展示 -->
            <div
              class="bg-white rounded-2xl overflow-hidden group hover:shadow-xl transition-all duration-300"
            >
              <div class="relative">
                <img
                  src="/img/photos/desk-3139127_640.jpg"
                  alt="服务优势"
                  class="w-full h-64 lg:h-80 object-cover group-hover:scale-105 transition-transform duration-500"
                />
                <div class="absolute top-4 left-4">
                  <div
                    class="w-12 h-12 bg-primary-500 rounded-xl flex items-center justify-center"
                  >
                    <UIcon name="i-heroicons-star" class="w-6 h-6 text-white" />
                  </div>
                </div>
              </div>
              <div class="p-8">
                <h3
                  class="text-2xl lg:text-3xl font-bold text-slate-900 mb-4 group-hover:text-slate-600 transition-colors"
                >
                  专业服务优势
                </h3>
                <p class="text-slate-600 mb-6 leading-relaxed">
                  赢睿科技凭借深厚的保险行业经验与强大的技术实力，为客户提供全方位的专业服务支持。
                </p>
                <div class="space-y-3 mb-6">
                  <div class="flex items-center space-x-3">
                    <div
                      class="w-2 h-2 bg-primary-500 rounded-full flex-shrink-0"
                    ></div>
                    <span class="text-sm text-slate-600"
                      >全场景覆盖，支持多种业务模式</span
                    >
                  </div>
                  <div class="flex items-center space-x-3">
                    <div
                      class="w-2 h-2 bg-primary-500 rounded-full flex-shrink-0"
                    ></div>
                    <span class="text-sm text-slate-600"
                      >专业团队，丰富行业经验</span
                    >
                  </div>
                  <div class="flex items-center space-x-3">
                    <div
                      class="w-2 h-2 bg-primary-500 rounded-full flex-shrink-0"
                    ></div>
                    <span class="text-sm text-slate-600"
                      >高效响应，快速解决问题</span
                    >
                  </div>
                </div>
                <UButton
                  class="bg-primary-500 hover:bg-primary-600 text-white px-8 py-3"
                  to="/services/legal-consulting"
                >
                  了解更多
                  <UIcon name="i-heroicons-arrow-right" class="w-4 h-4 ml-1" />
                </UButton>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
// 页面元数据
useHead({
  title: "专业服务体系 - 赢睿保险科技",
  meta: [
    {
      name: "description",
      content:
        "赢睿保险科技专业服务体系，包含咨询服务、运营服务、法律服务三大板块，为保险行业提供全方位的专业服务支持。",
    },
    {
      name: "keywords",
      content:
        "保险咨询服务,保险运营服务,货运保险法律咨询,IT等保合规,数智化转型,保险科技服务",
    },
  ],
});

// 服务分类
const serviceCategories = ref([
  { id: "consulting-services", name: "咨询服务" },
  { id: "operation-services", name: "运营服务" },
  { id: "legal-services", name: "法律服务" },
]);

// 当前激活的分类
const activeCategory = ref("consulting-services");

// 滚动到指定分类
const scrollToCategory = (categoryId: string) => {
  activeCategory.value = categoryId;
  const element = document.getElementById(categoryId);
  if (element) {
    const yOffset = -160; // 增加上边距，避免被sticky导航遮挡
    const y =
      element.getBoundingClientRect().top + window.pageYOffset + yOffset;
    window.scrollTo({ top: y, behavior: "smooth" });
  }
};

// 监听滚动事件更新激活分类
onMounted(() => {
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          activeCategory.value = entry.target.id;
        }
      });
    },
    {
      rootMargin: "-20% 0px -70% 0px",
    }
  );

  serviceCategories.value.forEach((category) => {
    const element = document.getElementById(category.id);
    if (element) {
      observer.observe(element);
    }
  });

  onUnmounted(() => {
    observer.disconnect();
  });
});
</script>

<style scoped>
/* 动画效果 */
.animate-fade-in-up {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.8s ease-out forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 平滑滚动 */
html {
  scroll-behavior: smooth;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}
</style>
