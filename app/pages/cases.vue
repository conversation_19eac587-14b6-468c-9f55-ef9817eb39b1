<template>
  <div class="min-h-screen bg-white">
    <!-- Hero Section -->
    <section
      class="relative overflow-hidden bg-primary-900 pt-32 lg:pt-60 pb-20 lg:pb-28"
    >
      <!-- 背景图像 -->
      <div class="absolute inset-0">
        <img
          src="/img/photos/meeting-2284501_640.jpg"
          alt="赢睿科技案例中心"
          class="w-full h-full object-cover"
        />
        <div class="absolute inset-0 bg-primary-900/75"></div>
      </div>

      <div class="relative container mx-auto px-6">
        <div class="max-w-5xl mx-auto text-center text-white">
          <!-- 面包屑导航 -->
          <nav class="mb-8">
            <ol
              class="flex justify-center items-center space-x-2 text-sm text-primary-300"
            >
              <li>
                <NuxtLink
                  to="/"
                  class="hover:text-primary-400 transition-colors"
                >
                  首页
                </NuxtLink>
              </li>
              <li>
                <UIcon name="i-heroicons-chevron-right" class="w-4 h-4" />
              </li>
              <li class="text-primary-400">案例中心</li>
            </ol>
          </nav>

          <h1
            class="text-4xl lg:text-6xl font-bold mb-8 leading-tight animate-fade-in-up"
            style="animation-delay: 0.2s"
          >
            赢睿科技<span class="text-primary-500">客户案例</span>
            <span
              class="block text-2xl lg:text-3xl font-light mt-4 text-primary-200"
            >
              全行业200+企业的信赖之选
            </span>
          </h1>
        </div>
      </div>
    </section>

    <!-- 案例展示区域 -->
    <section class="py-20 lg:py-28 bg-white">
      <div class="container mx-auto px-6">
        <div class="max-w-7xl mx-auto">
          <!-- 标题 -->
          <div class="text-center mb-16">
            <h2 class="text-4xl lg:text-5xl font-bold text-slate-900 mb-6">
              帮助千行百业的客户<span class="text-primary-500">数字化转型</span>
            </h2>
            <p class="text-xl text-slate-600 max-w-3xl mx-auto">
              通过AI技术驱动的保险科技解决方案，为不同行业的客户提供专业的数字化转型服务
            </p>
          </div>

          <!-- 分类标签 -->
          <div class="flex flex-wrap justify-center gap-4 mb-16">
            <button
              v-for="category in categories"
              :key="category.id"
              @click="activeCategory = category.id"
              :class="[
                'px-8 py-3 rounded-full text-lg font-medium transition-all duration-300',
                activeCategory === category.id
                  ? 'bg-primary-500 text-white shadow-lg'
                  : 'bg-primary-100 text-slate-600 hover:bg-primary-200',
              ]"
            >
              {{ category.name }}
            </button>
          </div>

          <!-- 案例卡片网格 -->
          <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div
              v-for="caseItem in filteredCases"
              :key="caseItem.id"
              class="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden border border-primary-100"
            >
              <!-- 案例图片 -->
              <div class="relative h-48 overflow-hidden">
                <img
                  :src="caseItem.image"
                  :alt="caseItem.title"
                  class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                />
                <div
                  class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"
                ></div>
                <!-- 客户Logo -->
                <div
                  class="absolute top-4 right-4 bg-white rounded-lg p-2 shadow-lg"
                >
                  <img
                    :src="caseItem.logo"
                    :alt="caseItem.client"
                    class="w-12 h-12 object-contain"
                  />
                </div>
              </div>

              <!-- 案例内容 -->
              <div class="p-6">
                <h3
                  class="text-xl font-bold text-slate-900 mb-3 group-hover:text-primary-500 transition-colors"
                >
                  {{ caseItem.title }}
                </h3>
                <p class="text-slate-600 mb-4 leading-relaxed">
                  {{ caseItem.description }}
                </p>

                <!-- 技术标签 -->
                <div class="flex flex-wrap gap-2 mb-6">
                  <span
                    v-for="tech in caseItem.technologies"
                    :key="tech"
                    class="px-3 py-1 bg-primary-50 text-slate-600 text-sm rounded-full"
                  >
                    {{ tech }}
                  </span>
                </div>

                <!-- 成果数据 -->
                <div class="grid grid-cols-2 gap-4 mb-6">
                  <div class="text-center">
                    <div class="text-2xl font-bold text-primary-500">
                      {{ caseItem.results.efficiency }}
                    </div>
                    <div class="text-sm text-primary-500">效率提升</div>
                  </div>
                  <div class="text-center">
                    <div class="text-2xl font-bold text-primary-500">
                      {{ caseItem.results.cost }}
                    </div>
                    <div class="text-sm text-primary-500">成本降低</div>
                  </div>
                </div>

                <!-- 查看详情按钮 -->
                <UButton
                  variant="ghost"
                  class="text-primary-500 w-full group-hover:bg-primary-50 group-hover:text-slate-600 transition-all duration-300"
                  @click="viewCaseDetail(caseItem)"
                >
                  获取更多案例详情
                  <UIcon name="i-heroicons-arrow-right" class="w-4 h-4 ml-2" />
                </UButton>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 合作伙伴展示 -->
    <section class="py-20 lg:py-28 bg-primary-50">
      <div class="container mx-auto px-6">
        <div class="max-w-7xl mx-auto">
          <!-- 标题 -->
          <div class="text-center mb-16">
            <h2 class="text-4xl lg:text-5xl font-bold text-slate-900 mb-6">
              200+企业选择我们，<span class="text-primary-500"
                >用实力完成客户托付</span
              >
            </h2>
            <p class="text-xl text-slate-600 max-w-4xl mx-auto">
              坚持以开放姿态，积极扶持创新，赢睿科技愿与合作伙伴一起，携手共进，持续为生态发展注入新能量
            </p>
          </div>

          <!-- 合作伙伴Logo网格 -->
          <div
            class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 items-center"
          >
            <div
              v-for="partner in partners"
              :key="partner.id"
              class="flex items-center justify-center p-6 bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300"
            >
              <img
                :src="partner.logo"
                :alt="partner.name"
                class="w-full h-12 object-contain filter grayscale hover:grayscale-0 transition-all duration-300"
              />
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA 区域 -->
    <section
      class="py-20 lg:py-28 bg-gradient-to-r from-primary-600 to-primary-700 relative overflow-hidden"
    >
      <!-- 背景装饰 -->
      <div class="absolute inset-0 opacity-10">
        <div
          class="absolute top-10 left-10 w-32 h-32 bg-white rounded-full"
        ></div>
        <div
          class="absolute bottom-10 right-10 w-24 h-24 bg-white rounded-full"
        ></div>
        <div
          class="absolute top-1/2 left-1/4 w-16 h-16 bg-white rounded-full"
        ></div>
      </div>

      <div class="relative container mx-auto px-6">
        <div class="max-w-5xl mx-auto text-center text-white">
          <h2 class="text-4xl lg:text-5xl font-bold mb-6">
            准备好开始了吗？立刻获得<span class="text-yellow-300"
              >数智化转型方案</span
            >
          </h2>
          <p class="text-xl text-primary-100">
            留下您的联系方式，即可获取1V1免费专家咨询服务
          </p>
        </div>
      </div>
    </section>

    <!-- 咨询抽屉组件 -->
    <InquiryDrawer
      v-model="isInquiryDrawerOpen"
      @submit="handleInquirySubmit"
    />
  </div>
</template>

<script setup lang="ts">
// SEO 配置
useHead({
  title: "客户案例 - 赢睿保险科技",
  meta: [
    {
      name: "description",
      content:
        "赢睿保险科技客户案例展示，涵盖保险公司、中介机构、物流企业等各行业AI数字化转型成功案例，200+企业信赖之选。",
    },
    {
      property: "og:title",
      content: "客户案例 - 赢睿保险科技",
    },
    {
      property: "og:description",
      content:
        "赢睿保险科技客户案例展示，涵盖保险公司、中介机构、物流企业等各行业AI数字化转型成功案例。",
    },
  ],
});

// 结构化数据
const structuredData = {
  "@context": "https://schema.org",
  "@type": "WebPage",
  name: "赢睿保险科技客户案例",
  description: "赢睿保险科技客户案例展示，涵盖各行业AI数字化转型成功案例",
  url: "https://winray.tech/cases",
};

useHead({
  script: [
    { type: "application/ld+json", innerHTML: JSON.stringify(structuredData) },
  ],
});

// 分类数据
const categories = ref([
  { id: "all", name: "全部案例" },
  { id: "insurance", name: "保险公司" },
  { id: "agency", name: "保险中介机构" },
  { id: "logistics", name: "物流企业" },
  { id: "mga", name: "MGA机构" },
]);

// 当前激活的分类
const activeCategory = ref("all");

// 案例数据
const cases = ref([
  {
    id: 1,
    category: "insurance",
    title: "太平洋保险货运险AI风控定价系统",
    description:
      "太平洋保险上海分公司面临货运险核保效率低、人工成本高的挑战。部署赢睿AI风控定价系统后，通过分析历史10万+货运险数据，为每单生成定制化风控报告。核保时间从平均4小时缩短至15分钟，定价准确率从78%提升至95%，赔付率降低18%。系统上线3个月，保费收入增长25%，核保人员工作效率提升80%。",
    client: "太平洋保险上海分公司",
    logo: "/img/clientslogo/2.webp",
    image: "/img/photos/office-620822_640.jpg",
    technologies: ["AI大语言模型", "智能风控", "动态定价"],
    results: {
      efficiency: "80%",
      cost: "18%",
    },
  },
  {
    id: 2,
    category: "insurance",
    title: "人保财险车险智能理赔系统",
    description:
      "人保财险北京分公司车险理赔业务量大，传统人工定损耗时长、成本高。赢睿智能理赔系统通过图像识别技术分析事故照片，自动生成定损报告。系统处理了超过5万起车险理赔案件，70%案件实现实时结案，理赔周期从平均7天缩短至1.5天，客户满意度从72%提升至96%。欺诈检测功能识别出1200起可疑案件，为公司挽回损失约800万元。",
    client: "人保财险北京分公司",
    logo: "/img/clientslogo/3.webp",
    image: "/img/photos/man-593333_640.jpg",
    technologies: ["智能理赔", "欺诈检测", "自动定损"],
    results: {
      efficiency: "70%",
      cost: "30%",
    },
  },
  {
    id: 3,
    category: "insurance",
    title: "平安保险综合业务中台建设",
    description:
      "平安保险深圳总部需要整合分散的业务系统，提升分支机构协同效率。赢睿业务中台统一了投保、核保、理赔、客服等核心流程，连接全国32个分支机构。系统上线后，业务处理效率提升40%，数据同步时间从小时级缩短至分钟级，客户投诉率降低35%。总部管理层可通过实时仪表盘监控各地业务动态，决策响应速度提升60%。",
    client: "平安保险深圳总部",
    logo: "/img/clientslogo/4.webp",
    image: "/img/photos/meeting-2284501_640.jpg",
    technologies: ["业务中台", "数据整合", "流程自动化"],
    results: {
      efficiency: "40%",
      cost: "25%",
    },
  },
  {
    id: 4,
    category: "agency",
    title: "华泰保险经纪全流程数字化改造",
    description:
      "华泰保险经纪公司原有系统分散，客户信息管理混乱，合规风险高。赢睿数字化解决方案通过B/S架构重构业务流程，建立统一客户档案，实现保单全生命周期管理。系统覆盖财产险、车险等8个险种，支持与15家保险公司API对接。改造后，运营效率提升30%，合规准备时间减少60%，客户续保率提升18%。",
    client: "华泰保险经纪公司",
    logo: "/img/clientslogo/5.webp",
    image: "/img/photos/job-5382501_640.jpg",
    technologies: ["数字化管理", "API对接", "合规保障"],
    results: {
      efficiency: "30%",
      cost: "40%",
    },
  },
  {
    id: 5,
    category: "agency",
    title: "中意保险代理移动展业平台",
    description:
      "中意保险代理公司代理人展业工具落后，客户服务效率低。赢睿移动展业平台为500+代理人提供在线出单、客户管理、产品推荐等功能。系统集成AI推荐引擎，根据客户画像精准推荐产品。上线6个月，代理人展业效率提升50%，保单转化率从12%提升至18%，客户满意度提升25%。移动端出单占比从30%提升至75%。",
    client: "中意保险代理公司",
    logo: "/img/clientslogo/7.webp",
    image: "/img/photos/desk-3139127_640.jpg",
    technologies: ["移动展业", "AI推荐", "精准营销"],
    results: {
      efficiency: "50%",
      cost: "15%",
    },
  },
  {
    id: 6,
    category: "mga",
    title: "中航安盟MGA智能运营平台",
    description:
      "中航安盟MGA机构产品设计周期长，与保险公司协作效率低。赢睿智能运营平台提供灵活的产品设计工具，支持快速配置保险参数和承保规则。系统整合市场数据，帮助开发家财险、商业空间保险等差异化产品。平台上线后，产品设计周期从3个月缩短至1.5个月，业务处理速度提升35%，与保险公司协作成本降低20%。",
    client: "中航安盟MGA机构",
    logo: "/img/clientslogo/8.webp",
    image: "/img/photos/laptop-3196481_640.jpg",
    technologies: ["智能风控", "产品设计", "数据驱动"],
    results: {
      efficiency: "35%",
      cost: "20%",
    },
  },
  {
    id: 7,
    category: "logistics",
    title: "顺丰速运货运保险平台",
    description:
      "顺丰速运日均处理货运保险需求超过10万单，传统投保流程复杂耗时。赢睿货运保险平台支持按票、按车一键投保，自动生成电子保单。系统集成顺丰物流数据，实现投保信息自动填充。平台上线后，投保效率提升50%，投保成本降低20%，客户投保体验显著改善。日均处理能力从8万单提升至12万单。",
    client: "顺丰速运集团",
    logo: "/img/clientslogo/9.webp",
    image: "/img/photos/job-5382501_640.jpg",
    technologies: ["一键投保", "智能理赔", "风险预测"],
    results: {
      efficiency: "50%",
      cost: "20%",
    },
  },
  {
    id: 8,
    category: "logistics",
    title: "中远海运跨境物流保险",
    description:
      "中远海运集团跨境业务覆盖50+国家，面临多国风险差异大、保障方案复杂的问题。赢睿跨境物流保险解决方案集成全球物流数据，结合AI分析提供精准风险预测。系统支持多国别保障方案，覆盖汇率波动、关税变化等风险。实施后，跨境理赔效率提升35%，客户满意度提高20%，海运保障成本降低15%。",
    client: "中远海运集团",
    logo: "/img/clientslogo/10.webp",
    image: "/img/photos/laptops-593296_640.jpg",
    technologies: ["跨境保险", "风险预测", "多国保障"],
    results: {
      efficiency: "35%",
      cost: "15%",
    },
  },
]);

// 合作伙伴数据
const partners = ref([
  { id: 1, name: "合作伙伴1", logo: "/img/clientslogo/11.webp" },
  { id: 2, name: "合作伙伴2", logo: "/img/clientslogo/12.webp" },
  { id: 3, name: "合作伙伴3", logo: "/img/clientslogo/13.webp" },
  { id: 4, name: "合作伙伴4", logo: "/img/clientslogo/14.webp" },
  { id: 5, name: "合作伙伴5", logo: "/img/clientslogo/15.webp" },
  { id: 6, name: "合作伙伴6", logo: "/img/clientslogo/16.webp" },
  { id: 7, name: "合作伙伴7", logo: "/img/clientslogo/17.webp" },
  { id: 8, name: "合作伙伴8", logo: "/img/clientslogo/27.webp" },
  { id: 9, name: "合作伙伴9", logo: "/img/clientslogo/28.webp" },
  { id: 10, name: "合作伙伴10", logo: "/img/clientslogo/29.webp" },
  { id: 11, name: "合作伙伴11", logo: "/img/clientslogo/30.webp" },
  { id: 12, name: "合作伙伴12", logo: "/img/clientslogo/31.webp" },
  { id: 13, name: "合作伙伴13", logo: "/img/clientslogo/32.webp" },
  { id: 14, name: "合作伙伴14", logo: "/img/clientslogo/33.webp" },
  { id: 15, name: "合作伙伴15", logo: "/img/clientslogo/34.webp" },
  { id: 16, name: "合作伙伴16", logo: "/img/clientslogo/35.webp" },
  { id: 17, name: "合作伙伴17", logo: "/img/clientslogo/36.webp" },
  { id: 18, name: "合作伙伴18", logo: "/img/clientslogo/39.webp" },
]);

// 过滤案例数据
const filteredCases = computed(() => {
  if (activeCategory.value === "all") {
    return cases.value;
  }
  return cases.value.filter(
    (caseItem) => caseItem.category === activeCategory.value
  );
});

// 控制咨询抽屉显示
const isInquiryDrawerOpen = ref(false);
const selectedCase = ref<any>(null);

// 查看案例详情
const viewCaseDetail = (caseItem: any) => {
  selectedCase.value = caseItem;
  isInquiryDrawerOpen.value = true;
  console.log("查看案例详情:", caseItem.title);
};

// 处理咨询表单提交
const handleInquirySubmit = (formData: any) => {
  console.log("咨询表单提交:", formData);
  console.log("相关案例:", selectedCase.value?.title);

  // 这里可以添加实际的表单提交逻辑
  // 例如发送到后端API或第三方服务
};

// 处理咨询点击
const handleConsultation = () => {
  // 这里可以跳转到联系页面或打开咨询表单
  console.log("立即咨询");
  // 可以添加实际的跳转逻辑
  // await navigateTo('/contact?type=consultation');
};
</script>

<style scoped>
.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
