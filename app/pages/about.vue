<template>
  <div class="min-h-screen bg-white">
    <!-- Hero Section -->
    <section
      class="relative overflow-hidden bg-primary-900 pt-32 lg:pt-60 pb-20 lg:pb-28"
    >
      <!-- 背景图像 -->
      <div class="absolute inset-0">
        <img
          src="/img/photos/office-620822_640.jpg"
          alt="赢睿科技办公环境"
          class="w-full h-full object-cover"
        />
        <div class="absolute inset-0 bg-primary-900/75"></div>
      </div>

      <div class="relative container mx-auto px-6">
        <div class="max-w-5xl mx-auto text-center text-white">
          <!-- 面包屑导航 -->
          <nav class="mb-8">
            <ol
              class="flex justify-center items-center space-x-2 text-sm text-primary-300"
            >
              <li>
                <NuxtLink
                  to="/"
                  class="hover:text-primary-400 transition-colors"
                >
                  首页
                </NuxtLink>
              </li>
              <li>
                <UIcon name="i-heroicons-chevron-right" class="w-4 h-4" />
              </li>
              <li class="text-primary-400">关于我们</li>
            </ol>
          </nav>

          <h1
            class="text-4xl lg:text-6xl font-bold mb-8 leading-tight animate-fade-in-up"
            style="animation-delay: 0.2s"
          >
            关于<span class="text-primary-500">赢睿科技</span>
            <span
              class="block text-2xl lg:text-3xl font-light mt-4 text-primary-200"
            >
              AI 驱动的保险科技领导者
            </span>
          </h1>
        </div>
      </div>
    </section>

    <!-- 公司概况 -->
    <section class="py-20 lg:py-28 bg-white">
      <div class="container mx-auto px-6">
        <div class="max-w-7xl mx-auto">
          <div class="grid lg:grid-cols-2 gap-16 items-center">
            <!-- 左侧内容 -->
            <div>
              <h2 class="text-4xl lg:text-5xl font-bold text-slate-900 mb-8">
                专注保险科技
                <span class="text-primary-500">创新发展</span>
              </h2>
              <div class="space-y-6 text-lg text-slate-600 leading-relaxed">
                <p>
                  赢睿保险科技是一家总部位于上海的保险科技公司，专注于保险行业系统软件开发与运营，致力于为保险公司、保险中介机构及物流行业提供高效、智能化、合规的数字化解决方案。
                </p>
                <p>
                  作为一家深耕财产保险领域的 SaaS
                  服务商，赢睿保险科技通过大数据、云计算和人工智能技术，为客户提供从业务管理到营销获客的全方位支持，助力行业实现数字化转型与业务增长。
                </p>
                <p>
                  我们运用前沿 AI
                  大语言模型、深度学习与大数据技术，为保险公司、中介机构及物流行业提供从核保到理赔的全流程智能化解决方案。让保险业务更高效、更智能、更合规。
                </p>
              </div>
            </div>

            <!-- 右侧图片 -->
            <div class="relative">
              <img
                src="/img/photos/meeting-2284501_640.jpg"
                alt="团队协作"
                class="w-full h-96 object-cover rounded-2xl shadow-xl"
              />
              <div
                class="absolute -bottom-6 -left-6 w-32 h-32 bg-primary-500 rounded-2xl flex items-center justify-center"
              >
                <div class="text-white text-center">
                  <div class="text-3xl font-bold">AI</div>
                  <div class="text-sm">驱动</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 核心价值观 -->
    <section class="py-20 lg:py-28 bg-primary-50">
      <div class="container mx-auto px-6">
        <div class="text-center mb-20">
          <h2 class="text-4xl lg:text-5xl font-bold text-slate-900 mb-6">
            我们的核心价值观
          </h2>
          <p class="text-xl text-slate-600 max-w-4xl mx-auto font-light">
            以客户为中心，以技术为驱动，以创新为使命
          </p>
        </div>

        <div class="grid lg:grid-cols-4 md:grid-cols-2 gap-8 max-w-7xl mx-auto">
          <!-- 专业权威 -->
          <div
            class="bg-white rounded-2xl p-8 text-center hover:shadow-xl transition-all duration-300 group"
          >
            <div
              class="w-20 h-20 bg-primary-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300"
            >
              <UIcon
                name="i-heroicons-academic-cap"
                class="w-10 h-10 text-white"
              />
            </div>
            <h3 class="text-2xl font-bold text-slate-900 mb-4">专业权威</h3>
            <p class="text-slate-600 leading-relaxed">
              体现保险科技的专业性，深耕行业多年，拥有丰富的实践经验
            </p>
          </div>

          <!-- 创新前瞻 -->
          <div
            class="bg-white rounded-2xl p-8 text-center hover:shadow-xl transition-all duration-300 group"
          >
            <div
              class="w-20 h-20 bg-primary-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300"
            >
              <UIcon
                name="i-heroicons-rocket-launch"
                class="w-10 h-10 text-white"
              />
            </div>
            <h3 class="text-2xl font-bold text-slate-900 mb-4">创新前瞻</h3>
            <p class="text-slate-600 leading-relaxed">
              强调 AI 技术的先进性，持续探索前沿技术在保险业的应用
            </p>
          </div>

          <!-- 客户导向 -->
          <div
            class="bg-white rounded-2xl p-8 text-center hover:shadow-xl transition-all duration-300 group"
          >
            <div
              class="w-20 h-20 bg-primary-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300"
            >
              <UIcon name="i-heroicons-heart" class="w-10 h-10 text-white" />
            </div>
            <h3 class="text-2xl font-bold text-slate-900 mb-4">客户导向</h3>
            <p class="text-slate-600 leading-relaxed">
              以解决客户痛点为核心，提供量身定制的解决方案
            </p>
          </div>

          <!-- 数据驱动 -->
          <div
            class="bg-white rounded-2xl p-8 text-center hover:shadow-xl transition-all duration-300 group"
          >
            <div
              class="w-20 h-20 bg-primary-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300"
            >
              <UIcon
                name="i-heroicons-chart-bar-square"
                class="w-10 h-10 text-white"
              />
            </div>
            <h3 class="text-2xl font-bold text-slate-900 mb-4">数据驱动</h3>
            <p class="text-slate-600 leading-relaxed">
              用具体数据支撑价值主张，让每个决策都有据可依
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- 技术实力 -->
    <section class="py-20 lg:py-28 bg-white">
      <div class="container mx-auto px-6">
        <div class="max-w-7xl mx-auto">
          <div class="grid lg:grid-cols-2 gap-16 items-center">
            <!-- 左侧图片 -->
            <div class="relative">
              <img
                src="/img/photos/laptop-3196481_640.jpg"
                alt="技术研发"
                class="w-full h-96 object-cover rounded-2xl shadow-xl"
              />
              <div
                class="absolute top-6 left-6 bg-white/90 backdrop-blur rounded-xl p-4"
              >
                <div class="flex items-center space-x-3">
                  <div
                    class="w-12 h-12 bg-primary-500 rounded-xl flex items-center justify-center"
                  >
                    <UIcon
                      name="i-heroicons-cpu-chip"
                      class="w-6 h-6 text-white"
                    />
                  </div>
                  <div>
                    <div class="font-bold text-slate-900">AI 技术</div>
                    <div class="text-sm text-slate-600">领先实力</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 右侧内容 -->
            <div>
              <h2 class="text-4xl lg:text-5xl font-bold text-slate-900 mb-8">
                强大的<span class="text-primary-500">技术实力</span>
              </h2>
              <div class="space-y-6">
                <!-- AI 大语言模型 -->
                <div class="flex items-start space-x-4">
                  <div
                    class="w-12 h-12 bg-primary-500 rounded-xl flex items-center justify-center flex-shrink-0 mt-1"
                  >
                    <UIcon
                      name="i-heroicons-chat-bubble-left-ellipsis"
                      class="w-6 h-6 text-white"
                    />
                  </div>
                  <div>
                    <h3 class="text-xl font-bold text-slate-900 mb-2">
                      AI 大语言模型
                    </h3>
                    <p class="text-slate-600">
                      自然语言理解与生成，智能对话与文档处理，为保险业务提供智能化支持
                    </p>
                  </div>
                </div>

                <!-- 深度学习算法 -->
                <div class="flex items-start space-x-4">
                  <div
                    class="w-12 h-12 bg-primary-500 rounded-xl flex items-center justify-center flex-shrink-0 mt-1"
                  >
                    <UIcon
                      name="i-heroicons-cpu-chip"
                      class="w-6 h-6 text-white"
                    />
                  </div>
                  <div>
                    <h3 class="text-xl font-bold text-slate-900 mb-2">
                      深度学习算法
                    </h3>
                    <p class="text-slate-600">
                      风险模式识别与预测，智能决策支持，提升业务处理效率和准确性
                    </p>
                  </div>
                </div>

                <!-- 图像识别技术 -->
                <div class="flex items-start space-x-4">
                  <div
                    class="w-12 h-12 bg-primary-500 rounded-xl flex items-center justify-center flex-shrink-0 mt-1"
                  >
                    <UIcon
                      name="i-heroicons-photo"
                      class="w-6 h-6 text-white"
                    />
                  </div>
                  <div>
                    <h3 class="text-xl font-bold text-slate-900 mb-2">
                      图像识别技术
                    </h3>
                    <p class="text-slate-600">
                      智能定损与材料审核，自动化理赔处理，大幅提升理赔效率
                    </p>
                  </div>
                </div>

                <!-- 知识图谱技术 -->
                <div class="flex items-start space-x-4">
                  <div
                    class="w-12 h-12 bg-primary-500 rounded-xl flex items-center justify-center flex-shrink-0 mt-1"
                  >
                    <UIcon
                      name="i-heroicons-puzzle-piece"
                      class="w-6 h-6 text-white"
                    />
                  </div>
                  <div>
                    <h3 class="text-xl font-bold text-slate-900 mb-2">
                      知识图谱技术
                    </h3>
                    <p class="text-slate-600">
                      智能问答与决策支持，知识管理优化，构建智能化业务体系
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 发展历程 -->
    <section class="py-20 lg:py-28 bg-primary-50">
      <div class="container mx-auto px-6">
        <div class="text-center mb-20">
          <h2 class="text-4xl lg:text-5xl font-bold text-slate-900 mb-6">
            发展历程
          </h2>
          <p class="text-xl text-slate-600 max-w-4xl mx-auto font-light">
            从创立至今，我们始终专注于保险科技创新
          </p>
        </div>

        <div class="max-w-5xl mx-auto">
          <div class="relative">
            <!-- 时间线 -->
            <div
              class="absolute left-1/2 transform -translate-x-1/2 w-1 bg-primary-200 h-full"
            ></div>

            <!-- 里程碑事件 -->
            <div class="space-y-16">
              <!-- 公司成立 -->
              <div class="relative flex items-center">
                <div class="w-1/2 pr-8 text-right">
                  <div
                    class="bg-white rounded-2xl p-6 shadow-lg border-l-4 border-primary-500"
                  >
                    <div class="text-slate-600 font-bold text-lg mb-2">
                      公司成立
                    </div>
                    <h3 class="text-xl font-bold text-slate-900 mb-3">
                      专注保险科技
                    </h3>
                    <p class="text-slate-600">
                      在上海正式成立，专注于保险行业系统软件开发与运营
                    </p>
                  </div>
                </div>
                <div
                  class="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-primary-500 rounded-full border-4 border-white"
                ></div>
                <div class="w-1/2 pl-8">
                  <div class="text-2xl font-bold text-primary-500">2019</div>
                </div>
              </div>

              <!-- AI 技术突破 -->
              <div class="relative flex items-center">
                <div class="w-1/2 pr-8">
                  <div class="text-2xl font-bold text-primary-500 text-right">
                    2021
                  </div>
                </div>
                <div
                  class="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-primary-500 rounded-full border-4 border-white"
                ></div>
                <div class="w-1/2 pl-8">
                  <div
                    class="bg-white rounded-2xl p-6 shadow-lg border-r-4 border-primary-500"
                  >
                    <div class="text-slate-600 font-bold text-lg mb-2">
                      AI 技术突破
                    </div>
                    <h3 class="text-xl font-bold text-slate-900 mb-3">
                      智能化转型
                    </h3>
                    <p class="text-slate-600">
                      成功将 AI 大语言模型应用于保险业务，推出首个智能核保系统
                    </p>
                  </div>
                </div>
              </div>

              <!-- 业务拓展 -->
              <div class="relative flex items-center">
                <div class="w-1/2 pr-8 text-right">
                  <div
                    class="bg-white rounded-2xl p-6 shadow-lg border-l-4 border-primary-500"
                  >
                    <div class="text-slate-600 font-bold text-lg mb-2">
                      业务拓展
                    </div>
                    <h3 class="text-xl font-bold text-slate-900 mb-3">
                      全面发展
                    </h3>
                    <p class="text-slate-600">
                      服务范围扩展至保险公司、中介机构和物流行业，客户遍布全国
                    </p>
                  </div>
                </div>
                <div
                  class="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-primary-500 rounded-full border-4 border-white"
                ></div>
                <div class="w-1/2 pl-8">
                  <div class="text-2xl font-bold text-primary-500">2023</div>
                </div>
              </div>

              <!-- 技术领先 -->
              <div class="relative flex items-center">
                <div class="w-1/2 pr-8">
                  <div class="text-2xl font-bold text-primary-500 text-right">
                    2024
                  </div>
                </div>
                <div
                  class="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-primary-500 rounded-full border-4 border-white"
                ></div>
                <div class="w-1/2 pl-8">
                  <div
                    class="bg-white rounded-2xl p-6 shadow-lg border-r-4 border-primary-500"
                  >
                    <div class="text-slate-600 font-bold text-lg mb-2">
                      技术领先
                    </div>
                    <h3 class="text-xl font-bold text-slate-900 mb-3">
                      行业领导者
                    </h3>
                    <p class="text-slate-600">
                      成为 AI
                      驱动的保险科技领导者，为行业数字化转型提供全方位支持
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 团队优势 -->
    <section class="py-20 lg:py-28 bg-white">
      <div class="container mx-auto px-6">
        <div class="text-center mb-20">
          <h2 class="text-4xl lg:text-5xl font-bold text-slate-900 mb-6">
            专业团队
          </h2>
          <p class="text-xl text-slate-600 max-w-4xl mx-auto font-light">
            汇聚保险、AI 技术、软件开发等领域的专业人才
          </p>
        </div>

        <div class="grid lg:grid-cols-3 md:grid-cols-2 gap-8 max-w-7xl mx-auto">
          <!-- 技术团队 -->
          <div
            class="bg-primary-50 rounded-2xl p-8 text-center hover:shadow-xl transition-all duration-300 group"
          >
            <div
              class="w-20 h-20 bg-primary-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300"
            >
              <UIcon
                name="i-heroicons-code-bracket"
                class="w-10 h-10 text-white"
              />
            </div>
            <h3 class="text-2xl font-bold text-slate-900 mb-4">技术研发团队</h3>
            <p class="text-slate-600 leading-relaxed mb-4">
              拥有丰富的 AI 技术和保险系统开发经验
            </p>
            <div class="text-slate-600 font-semibold">平均从业经验 8+ 年</div>
          </div>

          <!-- 业务团队 -->
          <div
            class="bg-primary-50 rounded-2xl p-8 text-center hover:shadow-xl transition-all duration-300 group"
          >
            <div
              class="w-20 h-20 bg-primary-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300"
            >
              <UIcon
                name="i-heroicons-briefcase"
                class="w-10 h-10 text-white"
              />
            </div>
            <h3 class="text-2xl font-bold text-slate-900 mb-4">业务专家团队</h3>
            <p class="text-slate-600 leading-relaxed mb-4">
              深度理解保险业务流程和行业发展趋势
            </p>
            <div class="text-slate-600 font-semibold">保险行业经验 10+ 年</div>
          </div>

          <!-- 服务团队 -->
          <div
            class="bg-primary-50 rounded-2xl p-8 text-center hover:shadow-xl transition-all duration-300 group"
          >
            <div
              class="w-20 h-20 bg-primary-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300"
            >
              <UIcon
                name="i-heroicons-user-group"
                class="w-10 h-10 text-white"
              />
            </div>
            <h3 class="text-2xl font-bold text-slate-900 mb-4">客户服务团队</h3>
            <p class="text-slate-600 leading-relaxed mb-4">
              提供专业的技术支持和售后服务保障
            </p>
            <div class="text-slate-600 font-semibold">24 小时响应服务</div>
          </div>
        </div>
      </div>
    </section>

    <!-- 联系信息 -->
    <section class="py-20 lg:py-28 bg-primary-50">
      <div class="container mx-auto px-6">
        <div class="text-center mb-16">
          <h2 class="text-4xl lg:text-5xl font-bold text-slate-900 mb-6">
            联系我们
          </h2>
          <p class="text-xl text-slate-600 max-w-4xl mx-auto font-light">
            欢迎与我们取得联系，共同探索保险科技的未来
          </p>
        </div>

        <div class="max-w-6xl mx-auto">
          <div class="grid lg:grid-cols-2 gap-12">
            <!-- 联系方式 -->
            <div class="space-y-8">
              <div class="flex items-start space-x-4">
                <div
                  class="w-16 h-16 bg-primary-500 rounded-xl flex items-center justify-center flex-shrink-0"
                >
                  <UIcon
                    name="i-heroicons-map-pin"
                    class="w-8 h-8 text-white"
                  />
                </div>
                <div>
                  <h3 class="text-xl font-bold text-slate-900 mb-2">
                    公司地址
                  </h3>
                  <p class="text-slate-600">
                    上海市杨浦区隆昌路 619 号 1 号楼 B102
                  </p>
                </div>
              </div>

              <div class="flex items-start space-x-4">
                <div
                  class="w-16 h-16 bg-primary-500 rounded-xl flex items-center justify-center flex-shrink-0"
                >
                  <UIcon name="i-heroicons-phone" class="w-8 h-8 text-white" />
                </div>
                <div>
                  <h3 class="text-xl font-bold text-slate-900 mb-2">
                    联系电话
                  </h3>
                  <p class="text-slate-600 text-lg font-semibold">
                    +86 181 2137 8388
                  </p>
                </div>
              </div>

              <div class="flex items-start space-x-4">
                <div
                  class="w-16 h-16 bg-primary-500 rounded-xl flex items-center justify-center flex-shrink-0"
                >
                  <UIcon
                    name="i-heroicons-envelope"
                    class="w-8 h-8 text-white"
                  />
                </div>
                <div>
                  <h3 class="text-xl font-bold text-slate-900 mb-2">
                    邮箱地址
                  </h3>
                  <p class="text-slate-600"><EMAIL></p>
                </div>
              </div>

              <div class="flex items-start space-x-4">
                <div
                  class="w-16 h-16 bg-primary-500 rounded-xl flex items-center justify-center flex-shrink-0"
                >
                  <UIcon name="i-heroicons-clock" class="w-8 h-8 text-white" />
                </div>
                <div>
                  <h3 class="text-xl font-bold text-slate-900 mb-2">
                    服务时间
                  </h3>
                  <p class="text-slate-600">
                    周一至周五 9:00-18:00<br />
                    24 小时技术支持热线
                  </p>
                </div>
              </div>
            </div>

            <!-- 公司图片 -->
            <div class="relative">
              <img
                src="/img/photos/desk-3139127_640.jpg"
                alt="办公环境"
                class="w-full h-96 object-cover rounded-2xl shadow-xl"
              />
              <div
                class="absolute bottom-6 left-6 bg-white/95 backdrop-blur rounded-xl p-4"
              >
                <div class="flex items-center space-x-3">
                  <img src="/img/winray-logo.png" alt="赢睿科技" class="h-10" />
                  <div>
                    <div class="font-bold text-slate-900">赢睿保险科技</div>
                    <div class="text-sm text-slate-600">AI 驱动 · 智慧保险</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
// 页面元数据
useHead({
  title: "关于我们 - 赢睿保险科技 | AI 驱动的保险科技领导者",
  meta: [
    {
      name: "description",
      content:
        "赢睿保险科技是一家专注于AI驱动的保险科技公司，总部位于上海。我们致力于通过人工智能、大数据、云计算技术为保险行业提供智能化解决方案，助力行业实现AI驱动的数字化转型。",
    },
    {
      name: "keywords",
      content:
        "赢睿保险科技,关于我们,保险科技公司,AI保险,人工智能保险,保险数字化转型,上海保险科技,保险科技创新",
    },
    {
      property: "og:title",
      content: "关于我们 - 赢睿保险科技 | AI 驱动的保险科技领导者",
    },
    {
      property: "og:description",
      content:
        "专注于AI驱动的保险科技公司，为保险行业提供智能化解决方案，助力数字化转型",
    },
    {
      property: "og:type",
      content: "website",
    },
  ],
});

// 结构化数据
const structuredData = {
  "@context": "https://schema.org",
  "@type": "Organization",
  name: "赢睿保险科技",
  url: "https://www.yingrtech.com",
  logo: "https://www.yingrtech.com/favicon.png",
  description:
    "赢睿保险科技是一家专注于AI驱动的保险科技公司，为保险行业提供智能化解决方案",
  address: {
    "@type": "PostalAddress",
    streetAddress: "隆昌路619号1号楼B102",
    addressLocality: "上海市",
    addressRegion: "杨浦区",
    addressCountry: "CN",
  },
  telephone: "+86 181 2137 8388",
  email: "<EMAIL>",
  foundingDate: "2019",
  industry: "保险科技",
  numberOfEmployees: "50-100",
};

useHead({
  script: [
    {
      type: "application/ld+json",
      innerHTML: JSON.stringify(structuredData),
    },
  ],
});
</script>

<style scoped>
/* 平滑滚动和动画效果 */
html {
  scroll-behavior: smooth;
}

/* 懒加载动画效果 */
.animate-fade-in-up {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.8s ease-out forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 时间线样式优化 */
.timeline-item:hover .timeline-content {
  transform: scale(1.02);
}

.timeline-content {
  transition: transform 0.3s ease;
}
</style>
