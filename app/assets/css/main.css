@import "tailwindcss";
@import "@nuxt/ui";

/* 全局字体和圆角配置 */
@layer base {
  /* 全局字体设置 */
  * {
    font-family: "Roboto", "PingFang SC", "Hiragino Sans GB", "Noto Sans",
      "Microsoft YaHei", sans-serif !important;
    border-radius: 0 !important;
  }

  /* 确保html和body也使用全局字体 */
  html,
  body {
    font-family: "Roboto", "PingFang SC", "Hiragino Sans GB", "Noto Sans",
      "Microsoft YaHei", sans-serif !important;
  }

  /* 覆盖常用的圆角类 */
  .rounded,
  .rounded-sm,
  .rounded-md,
  .rounded-lg,
  .rounded-xl,
  .rounded-2xl,
  .rounded-3xl,
  .rounded-full,
  .rounded-t,
  .rounded-r,
  .rounded-b,
  .rounded-l,
  .rounded-tl,
  .rounded-tr,
  .rounded-br,
  .rounded-bl {
    border-radius: 0 !important;
  }
}

@layer components {
  /* 确保按钮没有圆角 */
  button,
  .btn,
  input,
  textarea,
  select {
    border-radius: 0 !important;
  }

  /* 确保卡片和容器没有圆角 */
  .card,
  .modal,
  .dropdown,
  .tooltip {
    border-radius: 0 !important;
  }
}

/* 全局平滑滚动 */
html {
  scroll-behavior: smooth;
}

/* 锚点滚动偏移，考虑固定导航栏高度 */
section[id] {
  scroll-margin-top: 65px;
}

/* 自定义颜色配置 */
@theme {
  --color-primary-50: #f5f5f2;
  --color-primary-100: #eeeeeb;
  --color-primary-200: #e7e7de;
  --color-primary-300: #d4d4c8;
  --color-primary-400: #bfbfb0;
  --color-primary-500: #008891;
  --color-primary-600: #007a82;
  --color-primary-700: #00587a;
  --color-primary-800: #004866;
  --color-primary-900: #0f3057;
  --color-primary-950: #0a1f3a;

  --color-accent-50: #fef2f2;
  --color-accent-100: #fee2e2;
  --color-accent-200: #fecaca;
  --color-accent-300: #fca5a5;
  --color-accent-400: #f87171;
  --color-accent-500: #f76262;
  --color-accent-600: #ef4444;
  --color-accent-700: #dc2626;
  --color-accent-800: #b91c1c;
  --color-accent-900: #991b1b;
  --color-accent-950: #450a0a;

  --color-winray-50: #f0f8f9;
  --color-winray-100: #e1f1f3;
  --color-winray-200: #c3e3e7;
  --color-winray-300: #95cdd4;
  --color-winray-400: #5fb0bb;
  --color-winray-500: #008891;
  --color-winray-600: #007a82;
  --color-winray-700: #00587a;
  --color-winray-800: #004866;
  --color-winray-900: #0f3057;
  --color-winray-950: #0a1f3a;
}

/* 简单悬浮提升效果 */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
}
::-webkit-scrollbar-track {
  background: #e7e7de;
}
::-webkit-scrollbar-thumb {
  background: var(--color-primary-500);
  border-radius: 0;
}
::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary-600);
}

/* 回到顶部按钮样式 */
.back-to-top {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 50;
  transform: translateY(100px);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.back-to-top.visible {
  transform: translateY(0);
  opacity: 1;
}

/* Header 样式优化 */
header {
  transition: all 0.3s ease;
}

/* Header 悬停效果增强 */
header:hover {
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 下拉菜单动画优化 */
.group:hover .group-hover\:opacity-100 {
  animation: fadeInUp 0.2s ease-out forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
