---
description: Tailwind CSS 样式规范和设计系统，专注于保险科技品牌视觉一致性
globs: ["**/*.vue", "**/*.css", "**/tailwind.config.*"]
alwaysApply: false
---

# 样式和设计系统规范

## 设计系统概述

### 品牌色彩系统

```css
/* 主品牌色 - 科技绿 */
:root {
  --color-primary-50: #ecfdf5;
  --color-primary-100: #d1fae5;
  --color-primary-200: #a7f3d0;
  --color-primary-300: #6ee7b7;
  --color-primary-400: #34d399;
  --color-primary-500: #10b981; /* 主品牌色 */
  --color-primary-600: #059669;
  --color-primary-700: #047857;
  --color-primary-800: #065f46;
  --color-primary-900: #064e3b;
}

/* 辅助色系 */
:root {
  --color-secondary-50: #f0f9ff;
  --color-secondary-500: #3b82f6; /* 信任蓝 */
  --color-accent-500: #f59e0b; /* 警示橙 */
  --color-success-500: #10b981; /* 成功绿 */
  --color-warning-500: #f59e0b; /* 警告橙 */
  --color-error-500: #ef4444; /* 错误红 */
}

/* 中性色系 */
:root {
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;
  --color-gray-950: #030712;
}
```

### 字体系统

```css
/* 字体族定义 */
:root {
  --font-sans: "Inter", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei",
    sans-serif;
  --font-serif: "Merriweather", "Georgia", "Times New Roman", serif;
  --font-mono: "JetBrains Mono", "Fira Code", "Consolas", monospace;
}

/* 字体大小系统 */
:root {
  --text-xs: 0.75rem; /* 12px */
  --text-sm: 0.875rem; /* 14px */
  --text-base: 1rem; /* 16px */
  --text-lg: 1.125rem; /* 18px */
  --text-xl: 1.25rem; /* 20px */
  --text-2xl: 1.5rem; /* 24px */
  --text-3xl: 1.875rem; /* 30px */
  --text-4xl: 2.25rem; /* 36px */
  --text-5xl: 3rem; /* 48px */
  --text-6xl: 3.75rem; /* 60px */
  --text-7xl: 4.5rem; /* 72px */
}

/* 行高系统 */
:root {
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;
}
```

### 间距系统

```css
/* 间距系统 (基于 8px 网格) */
:root {
  --spacing-0: 0;
  --spacing-1: 0.25rem; /* 4px */
  --spacing-2: 0.5rem; /* 8px */
  --spacing-3: 0.75rem; /* 12px */
  --spacing-4: 1rem; /* 16px */
  --spacing-5: 1.25rem; /* 20px */
  --spacing-6: 1.5rem; /* 24px */
  --spacing-8: 2rem; /* 32px */
  --spacing-10: 2.5rem; /* 40px */
  --spacing-12: 3rem; /* 48px */
  --spacing-16: 4rem; /* 64px */
  --spacing-20: 5rem; /* 80px */
  --spacing-24: 6rem; /* 96px */
  --spacing-32: 8rem; /* 128px */
}
```

## Tailwind CSS 使用规范

### 类名组织规范

```vue
<template>
  <!-- 类名按功能分组，使用换行提高可读性 -->
  <div
    class="
    /* 布局 */
    flex items-center justify-between
    /* 尺寸 */
    w-full h-16 
    /* 间距 */
    px-6 py-4
    /* 背景和边框 */
    bg-white border-b border-gray-200
    /* 阴影和效果 */
    shadow-sm
    /* 响应式 */
    lg:px-8 lg:h-20
    /* 状态 */
    hover:shadow-md
    /* 过渡 */
    transition-all duration-300
  "
  >
    <h1
      class="
      /* 字体 */
      text-2xl font-bold 
      /* 颜色 */
      text-gray-900
      /* 响应式 */
      lg:text-3xl
    "
    >
      {{ title }}
    </h1>
  </div>
</template>
```

### 组件样式模式

```vue
<template>
  <!-- 产品卡片组件样式示例 -->
  <article class="product-card group">
    <!-- 卡片容器 -->
    <div class="product-card__container">
      <!-- 头部区域 -->
      <header class="product-card__header">
        <div class="product-card__icon-wrapper">
          <UIcon :name="icon" class="product-card__icon" />
        </div>
        <div class="product-card__title-group">
          <h3 class="product-card__title">{{ title }}</h3>
          <p class="product-card__category">{{ category }}</p>
        </div>
      </header>

      <!-- 内容区域 -->
      <main class="product-card__content">
        <p class="product-card__description">{{ description }}</p>

        <!-- 特性列表 -->
        <ul class="product-card__features">
          <li
            v-for="feature in features"
            :key="feature"
            class="product-card__feature"
          >
            <UIcon
              name="i-heroicons-check-circle"
              class="product-card__feature-icon"
            />
            <span class="product-card__feature-text">{{ feature }}</span>
          </li>
        </ul>
      </main>

      <!-- 底部操作区域 -->
      <footer class="product-card__footer">
        <UButton class="product-card__cta" @click="handleLearnMore">
          了解详情 →
        </UButton>
      </footer>
    </div>
  </article>
</template>

<style scoped>
/* 使用 @apply 指令组织复用样式 */
.product-card {
  @apply relative overflow-hidden;
}

.product-card__container {
  @apply bg-white rounded-xl shadow-lg hover:shadow-xl
         transition-all duration-300 ease-out
         border border-gray-100 hover:border-primary-200;
}

.product-card:hover .product-card__container {
  @apply transform -translate-y-1;
}

.product-card__header {
  @apply p-6 border-b border-gray-100
         bg-gradient-to-r from-primary-50 to-primary-100;
}

.product-card__icon-wrapper {
  @apply w-12 h-12 bg-primary-500 rounded-lg
         flex items-center justify-center mb-4
         group-hover:scale-110 transition-transform duration-300;
}

.product-card__icon {
  @apply w-6 h-6 text-white;
}

.product-card__title-group {
  @apply space-y-1;
}

.product-card__title {
  @apply text-xl font-bold text-gray-900 
         group-hover:text-primary-700 transition-colors;
}

.product-card__category {
  @apply text-sm font-medium text-primary-600;
}

.product-card__content {
  @apply p-6 space-y-4;
}

.product-card__description {
  @apply text-gray-600 leading-relaxed text-sm;
}

.product-card__features {
  @apply space-y-2;
}

.product-card__feature {
  @apply flex items-start space-x-2;
}

.product-card__feature-icon {
  @apply w-4 h-4 text-primary-500 mt-0.5 flex-shrink-0;
}

.product-card__feature-text {
  @apply text-sm text-gray-700;
}

.product-card__footer {
  @apply p-6 pt-0;
}

.product-card__cta {
  @apply w-full bg-primary-500 hover:bg-primary-600 
         text-white font-semibold py-3 px-4 rounded-lg
         transition-all duration-200
         focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
}
</style>
```

### 响应式设计规范

```vue
<template>
  <!-- 移动端优先的响应式设计 -->
  <section class="hero-section">
    <div class="hero-section__container">
      <!-- 标题 - 响应式字体大小 -->
      <h1 class="hero-section__title">
        <span class="hero-section__highlight">AI</span> 赋能保险科技
        <span class="hero-section__subtitle">智慧驱动业务增长</span>
      </h1>

      <!-- 描述文本 - 响应式间距和字体 -->
      <p class="hero-section__description">
        运用前沿 AI
        大语言模型、深度学习与大数据技术，为保险公司、中介机构及物流行业提供从核保到理赔的全流程智能化解决方案。
      </p>

      <!-- 按钮组 - 响应式布局 -->
      <div class="hero-section__actions">
        <UButton class="hero-section__primary-btn"> 立即体验 AI 核保 </UButton>
        <UButton variant="outline" class="hero-section__secondary-btn">
          获取解决方案
        </UButton>
      </div>
    </div>
  </section>
</template>

<style scoped>
/* 移动端优先响应式设计 */
.hero-section {
  @apply py-16 px-4
         lg:py-24 lg:px-8
         xl:py-32;
}

.hero-section__container {
  @apply max-w-4xl mx-auto text-center;
}

.hero-section__title {
  @apply text-4xl font-bold text-gray-900 mb-6
         sm:text-5xl sm:mb-8
         lg:text-6xl lg:mb-10
         xl:text-7xl;
}

.hero-section__highlight {
  @apply text-primary-500;
}

.hero-section__subtitle {
  @apply block text-2xl font-light text-gray-600 mt-4
         sm:text-3xl sm:mt-6
         lg:text-4xl lg:mt-8;
}

.hero-section__description {
  @apply text-base text-gray-600 mb-8 leading-relaxed
         sm:text-lg sm:mb-10
         lg:text-xl lg:mb-12
         xl:text-2xl;
}

.hero-section__actions {
  @apply flex flex-col space-y-4
         sm:flex-row sm:space-y-0 sm:space-x-4 sm:justify-center
         lg:space-x-6;
}

.hero-section__primary-btn {
  @apply w-full px-8 py-4 text-base font-semibold
         sm:w-auto sm:px-10
         lg:px-12 lg:py-5 lg:text-lg;
}

.hero-section__secondary-btn {
  @apply w-full px-8 py-4 text-base font-semibold
         sm:w-auto sm:px-10  
         lg:px-12 lg:py-5 lg:text-lg;
}

/* 平板端优化 */
@media (min-width: 768px) and (max-width: 1023px) {
  .hero-section {
    @apply py-20;
  }

  .hero-section__title {
    @apply text-5xl mb-8;
  }

  .hero-section__subtitle {
    @apply text-3xl mt-6;
  }
}
</style>
```

## 动画和交互效果

### 过渡动画规范

```vue
<template>
  <!-- 标准过渡效果 -->
  <div class="interactive-card">
    <!-- 悬停效果 -->
    <div class="card-hover-effect">
      <h3>AI 风控定价系统</h3>
      <p>基于大语言模型的智能定价引擎</p>
    </div>

    <!-- 点击反馈 -->
    <button class="action-button">
      <span class="button-text">了解详情</span>
      <UIcon name="i-heroicons-arrow-right" class="button-icon" />
    </button>
  </div>
</template>

<style scoped>
/* 标准过渡时间 */
.interactive-card {
  @apply transition-all duration-300 ease-out;
}

/* 悬停效果 */
.card-hover-effect {
  @apply transform transition-all duration-300 ease-out
         hover:scale-105 hover:shadow-xl
         active:scale-95;
}

/* 按钮交互效果 */
.action-button {
  @apply relative overflow-hidden
         bg-primary-500 hover:bg-primary-600
         text-white font-semibold py-3 px-6 rounded-lg
         transition-all duration-200 ease-out
         focus:ring-2 focus:ring-primary-500 focus:ring-offset-2
         active:transform active:scale-95;
}

.button-text {
  @apply transition-transform duration-200 ease-out;
}

.button-icon {
  @apply ml-2 transition-transform duration-200 ease-out
         group-hover:translate-x-1;
}

/* 加载状态动画 */
.loading-spinner {
  @apply animate-spin;
}

.loading-pulse {
  @apply animate-pulse;
}

/* 淡入动画 */
.fade-in {
  @apply opacity-0 translate-y-4
         transition-all duration-500 ease-out;
}

.fade-in.visible {
  @apply opacity-100 translate-y-0;
}

/* 滑入动画 */
.slide-in-left {
  @apply transform -translate-x-full opacity-0
         transition-all duration-500 ease-out;
}

.slide-in-left.visible {
  @apply translate-x-0 opacity-100;
}
</style>
```

### 自定义动画效果

```vue
<template>
  <div class="ai-showcase">
    <!-- 数据指标动画 -->
    <div class="metrics-animation">
      <div class="metric-item" v-for="metric in metrics" :key="metric.id">
        <div class="metric-value" :class="{ 'animate-count-up': isVisible }">
          {{ metric.value }}
        </div>
        <div class="metric-label">{{ metric.label }}</div>
      </div>
    </div>

    <!-- 打字机效果 -->
    <div class="typewriter-effect">
      <h2 class="typewriter-text">{{ displayText }}</h2>
    </div>

    <!-- 渐进式显示效果 -->
    <div class="stagger-animation">
      <div
        v-for="(item, index) in features"
        :key="item.id"
        class="feature-item"
        :style="{ animationDelay: `${index * 0.1}s` }"
      >
        {{ item.name }}
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 数字递增动画 */
@keyframes countUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-count-up {
  animation: countUp 0.8s ease-out forwards;
}

/* 打字机效果 */
@keyframes typewriter {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

@keyframes blink {
  from,
  to {
    border-color: transparent;
  }
  50% {
    border-color: #10b981;
  }
}

.typewriter-text {
  @apply overflow-hidden border-r-2 border-primary-500 whitespace-nowrap;
  animation: typewriter 3s steps(40, end), blink 0.75s step-end infinite;
}

/* 错开显示动画 */
@keyframes staggerFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.feature-item {
  @apply opacity-0;
  animation: staggerFadeIn 0.6s ease-out forwards;
}

/* 脉冲效果 */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.pulse-effect {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 浮动效果 */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.float-effect {
  animation: float 3s ease-in-out infinite;
}

/* 波纹效果 */
@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

.ripple-effect::after {
  @apply content-[''] absolute inset-0 rounded-full
         bg-primary-500 opacity-0 pointer-events-none;
  animation: ripple 0.6s linear;
}
</style>
```

## 暗色模式支持

### 暗色主题配置

```vue
<template>
  <div class="theme-aware-component">
    <!-- 自适应主题的卡片 -->
    <div class="adaptive-card">
      <h3 class="adaptive-title">AI 保险科技解决方案</h3>
      <p class="adaptive-description">智能化转型，数据驱动决策</p>
      <button class="adaptive-button">了解更多</button>
    </div>
  </div>
</template>

<style scoped>
/* 亮色模式样式 */
.adaptive-card {
  @apply bg-white border border-gray-200 text-gray-900
         shadow-lg hover:shadow-xl;
}

.adaptive-title {
  @apply text-gray-900 font-bold text-xl mb-4;
}

.adaptive-description {
  @apply text-gray-600 mb-6;
}

.adaptive-button {
  @apply bg-primary-500 hover:bg-primary-600 text-white
         px-6 py-3 rounded-lg font-semibold
         transition-colors duration-200;
}

/* 暗色模式样式 */
@media (prefers-color-scheme: dark) {
  .adaptive-card {
    @apply bg-gray-800 border-gray-700 text-white
           shadow-xl hover:shadow-2xl;
  }

  .adaptive-title {
    @apply text-white;
  }

  .adaptive-description {
    @apply text-gray-300;
  }

  .adaptive-button {
    @apply bg-primary-600 hover:bg-primary-700;
  }
}

/* 手动暗色模式类 */
.dark .adaptive-card {
  @apply bg-gray-800 border-gray-700 text-white;
}

.dark .adaptive-title {
  @apply text-white;
}

.dark .adaptive-description {
  @apply text-gray-300;
}

.dark .adaptive-button {
  @apply bg-primary-600 hover:bg-primary-700;
}
</style>
```

## 可访问性样式规范

### 对比度和可读性

```vue
<style scoped>
/* 确保足够的对比度 */
.accessible-text {
  /* WCAG AA 标准：4.5:1 对比度 */
  @apply text-gray-900;
  background-color: #ffffff;
}

.accessible-link {
  /* 链接颜色确保可访问性 */
  @apply text-primary-600 hover:text-primary-700
         underline underline-offset-2
         focus:ring-2 focus:ring-primary-500 focus:ring-offset-2
         focus:outline-none;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .accessible-text {
    @apply text-black;
    background-color: #ffffff;
  }

  .accessible-link {
    @apply text-blue-800 border-b-2 border-blue-800;
  }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 焦点指示器 */
.focus-visible {
  @apply ring-2 ring-primary-500 ring-offset-2 outline-none;
}

/* 跳过链接 */
.skip-link {
  @apply absolute -top-full left-4 z-50
         bg-primary-500 text-white px-4 py-2 rounded
         focus:top-4 transition-all duration-200;
}
</style>
```

### 表单样式可访问性

```vue
<template>
  <form class="accessible-form">
    <div class="form-group">
      <label for="name" class="form-label required">
        姓名
        <span class="sr-only">必填</span>
      </label>
      <input
        id="name"
        type="text"
        class="form-input"
        :class="{ 'form-input--error': errors.name }"
        aria-describedby="name-error"
        aria-invalid="errors.name ? 'true' : 'false'"
        required
      />
      <div v-if="errors.name" id="name-error" class="form-error" role="alert">
        {{ errors.name }}
      </div>
    </div>
  </form>
</template>

<style scoped>
.accessible-form {
  @apply space-y-6;
}

.form-group {
  @apply space-y-2;
}

.form-label {
  @apply block text-sm font-medium text-gray-700
         mb-2;
}

.form-label.required::after {
  @apply text-red-500 ml-1;
  content: "*";
}

.form-input {
  @apply w-full px-4 py-3 border border-gray-300 rounded-lg
         focus:ring-2 focus:ring-primary-500 focus:border-primary-500
         transition-colors duration-200
         placeholder:text-gray-400;
}

.form-input--error {
  @apply border-red-500 focus:ring-red-500 focus:border-red-500;
}

.form-error {
  @apply text-sm text-red-600 mt-1
         flex items-center space-x-1;
}

.sr-only {
  @apply absolute w-px h-px p-0 -m-px overflow-hidden
         whitespace-nowrap border-0;
  clip: rect(0, 0, 0, 0);
}
</style>
```

## 性能优化样式规范

### CSS 优化策略

```vue
<style scoped>
/* 使用 CSS 自定义属性提高性能 */
.performance-optimized {
  /* 避免重复的颜色值 */
  --primary-color: theme("colors.primary.500");
  --hover-color: theme("colors.primary.600");
  --text-color: theme("colors.gray.900");

  color: var(--text-color);
  background-color: var(--primary-color);
  transition: background-color 0.2s ease;
}

.performance-optimized:hover {
  background-color: var(--hover-color);
}

/* 使用 transform 而不是改变 layout 属性 */
.smooth-animation {
  @apply transition-transform duration-300 ease-out;
  will-change: transform;
}

.smooth-animation:hover {
  @apply transform scale-105;
}

/* 避免昂贵的 CSS 属性 */
.efficient-shadow {
  /* 使用 box-shadow 而不是 filter: drop-shadow */
  @apply shadow-lg;
}

/* 优化重绘和重排 */
.layout-stable {
  /* 使用 aspect-ratio 避免布局偏移 */
  aspect-ratio: 16 / 9;
  @apply w-full;
}

/* GPU 加速 */
.gpu-accelerated {
  @apply transform-gpu;
  will-change: transform;
}
</style>
```

### 关键 CSS 内联

```vue
<script setup lang="ts">
// 关键路径 CSS 提取
const criticalCSS = `
  /* 首屏关键样式 */
  .hero-section {
    padding: 4rem 1rem;
    text-align: center;
    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
  }
  
  .hero-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #111827;
    margin-bottom: 1.5rem;
  }
  
  .hero-highlight {
    color: #10b981;
  }
  
  /* 防止布局偏移 */
  .content-placeholder {
    min-height: 200px;
  }
`;

// 在页面头部注入关键 CSS
useHead({
  style: [
    {
      innerHTML: criticalCSS,
      type: "text/css",
    },
  ],
});
</script>
```
