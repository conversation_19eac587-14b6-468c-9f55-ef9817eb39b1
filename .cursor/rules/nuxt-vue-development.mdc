---
description: Nuxt 3 和 Vue 3 开发最佳实践规范
globs: ["**/*.vue", "**/*.ts", "**/nuxt.config.ts"]
alwaysApply: false
---

# Nuxt 3 + Vue 3 开发规范

## Nuxt 3 特定规范

### 页面组件 (Pages)

- 使用 `pages/` 目录实现自动路由
- 页面组件文件名使用 kebab-case
- 使用 `definePageMeta()` 定义页面元数据
- 合理使用 `useHead()` 进行 SEO 优化

```vue
<script setup lang="ts">
// 页面元数据定义
definePageMeta({
  title: "AI风控定价系统 - 赢睿保险科技",
  description: "基于AI大语言模型的智能风控定价解决方案",
});

// SEO 优化
useHead({
  title: "AI风控定价系统 - 赢睿保险科技",
  meta: [
    { name: "description", content: "..." },
    { name: "keywords", content: "..." },
  ],
});
</script>
```

### 布局系统 (Layouts)

- 使用 `layouts/` 目录定义布局
- 默认布局文件名为 `default.vue`
- 在页面中使用 `<NuxtLayout>` 指定布局

### 组件自动导入

- `components/` 目录下的组件自动导入
- 组件名使用 PascalCase
- 避免命名冲突，使用描述性名称

### 服务端渲染 (SSR)

- 考虑 SSR 兼容性，避免仅客户端的 API
- 使用 `process.client` 检查客户端环境
- 合理使用 `<ClientOnly>` 组件

## Vue 3 组合式 API 规范

### 脚本设置

```vue
<script setup lang="ts">
// 导入顺序：
// 1. Vue 相关
// 2. Nuxt 相关
// 3. 第三方库
// 4. 本地组件和工具

import { ref, computed, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useHead } from "@unhead/vue";

// Props 定义
interface Props {
  title: string;
  description?: string;
}

const props = withDefaults(defineProps<Props>(), {
  description: "默认描述",
});

// 响应式数据
const isLoading = ref(false);
const menuItems = ref([]);

// 计算属性
const formattedTitle = computed(() => {
  return `${props.title} - 赢睿保险科技`;
});

// 生命周期钩子
onMounted(() => {
  // 初始化逻辑
});
</script>
```

### 响应式数据管理

- 使用 `ref()` 处理基本类型数据
- 使用 `reactive()` 处理对象类型数据
- 合理使用 `computed()` 创建计算属性
- 避免在模板中使用复杂的表达式

### 事件处理

```vue
<script setup lang="ts">
// 事件处理函数使用 handle 前缀
const handleSubmit = async () => {
  try {
    isLoading.value = true;
    // 处理提交逻辑
  } catch (error) {
    console.error("提交失败:", error);
  } finally {
    isLoading.value = false;
  }
};

// 菜单操作函数
const handleMenuToggle = (menuId: string) => {
  activeMenu.value = activeMenu.value === menuId ? null : menuId;
};
</script>

<template>
  <button @click="handleSubmit" :disabled="isLoading">
    {{ isLoading ? "提交中..." : "提交" }}
  </button>
</template>
```

## TypeScript 集成规范

### 接口定义

```typescript
// 定义清晰的接口
interface ProductInfo {
  id: string;
  name: string;
  description: string;
  features: string[];
  isActive: boolean;
}

interface MenuConfig {
  id: string;
  name: string;
  link: string;
  children?: MenuCategory[];
}

interface MenuCategory {
  title: string;
  items: MenuItem[];
}
```

### 组件 Props 类型

```vue
<script setup lang="ts">
// Props 接口定义
interface ProductCardProps {
  product: ProductInfo;
  showDescription?: boolean;
  onSelect?: (id: string) => void;
}

// 使用 withDefaults 设置默认值
const props = withDefaults(defineProps<ProductCardProps>(), {
  showDescription: true,
});

// Emits 定义
const emit = defineEmits<{
  select: [id: string];
  update: [product: ProductInfo];
}>();
</script>
```

## 性能优化最佳实践

### 懒加载和异步组件

```vue
<script setup lang="ts">
// 异步组件导入
const LazyComponent = defineAsyncComponent(
  () => import("~/components/HeavyComponent.vue")
);

// 条件渲染重型组件
const showHeavyComponent = ref(false);
</script>

<template>
  <div>
    <!-- 懒加载组件 -->
    <LazyComponent v-if="showHeavyComponent" />

    <!-- 使用 Suspense 处理异步组件 -->
    <Suspense>
      <template #default>
        <AsyncDataComponent />
      </template>
      <template #fallback>
        <div>加载中...</div>
      </template>
    </Suspense>
  </div>
</template>
```

### 图像优化

```vue
<template>
  <!-- 使用 NuxtImg 进行图像优化 -->
  <NuxtImg
    src="/assets/img/hero-bg.jpg"
    alt="AI保险科技背景"
    width="1200"
    height="600"
    format="webp"
    loading="lazy"
    class="w-full h-auto"
  />
</template>
```

## 错误处理规范

### 异步操作错误处理

```vue
<script setup lang="ts">
const handleAsyncOperation = async () => {
  try {
    isLoading.value = true;
    const result = await apiCall();
    // 处理成功结果
  } catch (error) {
    console.error("操作失败:", error);
    // 显示用户友好的错误信息
    errorMessage.value = "操作失败，请稍后重试";
  } finally {
    isLoading.value = false;
  }
};
</script>
```

### 表单验证

```vue
<script setup lang="ts">
interface FormData {
  name: string;
  email: string;
  phone: string;
}

const formData = reactive<FormData>({
  name: "",
  email: "",
  phone: "",
});

const errors = reactive<Partial<FormData>>({});

const validateForm = (): boolean => {
  // 清空之前的错误
  Object.keys(errors).forEach((key) => delete errors[key]);

  if (!formData.name.trim()) {
    errors.name = "请输入姓名";
  }

  if (!formData.email.includes("@")) {
    errors.email = "请输入有效的邮箱地址";
  }

  return Object.keys(errors).length === 0;
};
</script>
```

## 可访问性 (A11y) 要求

- 为所有交互元素提供适当的 ARIA 标签
- 确保键盘导航的可用性
- 保持足够的颜色对比度
- 为图像提供有意义的 alt 文本

```vue
<template>
  <!-- 可访问的按钮 -->
  <button
    :aria-label="isMenuOpen ? '关闭菜单' : '打开菜单'"
    :aria-expanded="isMenuOpen"
    @click="toggleMenu"
  >
    <UIcon :name="isMenuOpen ? 'i-heroicons-x-mark' : 'i-heroicons-bars-3'" />
  </button>

  <!-- 可访问的导航 -->
  <nav role="navigation" aria-label="主导航">
    <ul>
      <li v-for="item in menuItems" :key="item.id">
        <NuxtLink
          :to="item.link"
          :aria-current="isCurrentPage(item.link) ? 'page' : undefined"
        >
          {{ item.name }}
        </NuxtLink>
      </li>
    </ul>
  </nav>
</template>
```
