---
description: 赢睿科技官网项目总体开发规范和指导原则
globs:
alwaysApply: true
---

# 赢睿科技官网项目开发规范

这是一个基于 Nuxt 3 的赢睿保险科技公司官网项目，专注于 AI 保险科技解决方案展示。

## 项目技术栈

- **框架**: Nuxt 3 + Vue 3 + TypeScript
- **UI 库**: @nuxt/ui (基于 Headless UI + Tailwind CSS)
- **样式**: Tailwind CSS 4.x
- **图像处理**: @nuxt/image
- **动画**: @vueuse/motion
- **构建工具**: Vite

## 核心开发原则

### 1. 中文优先原则

- 所有用户界面文本使用简体中文
- 代码注释使用中文，提高团队协作效率
- 变量和函数命名使用英文，但注释说明用中文

### 2. AI 保险科技主题一致性

- 保持 AI 和保险科技的专业形象
- 强调技术先进性和行业专业性
- 使用品牌色彩：主色 #10b981 (primary-500)

### 3. 响应式设计优先

- 移动端优先的设计理念
- 确保在所有设备上的良好体验
- 使用 Tailwind 的响应式类名

### 4. 性能优化

- 使用 @nuxt/image 进行图像优化
- 实施代码分割和懒加载
- 优化 SEO 和 Core Web Vitals

## 代码质量标准

### TypeScript 使用

- 严格的类型检查
- 为所有 props 和数据定义接口
- 避免使用 any 类型

### Vue 3 组合式 API

- 优先使用 Composition API
- 使用 `<script setup>` 语法
- 合理使用响应式引用 (ref/reactive)

### 组件开发规范

- 组件名使用 PascalCase
- 文件名使用 kebab-case
- 单一职责原则，保持组件简洁

## 项目结构理解

```
app/
├── assets/          # 静态资源
├── components/      # 可复用组件
├── layouts/         # 布局组件
├── pages/           # 页面组件（自动路由）
├── app.vue         # 根组件
└── ...

赢睿科技官网栏目文案/   # 内容文案参考
├── 产品介绍/
├── 解决方案/
├── 服务体系/
└── ...
```

## 内容管理策略

- 参考 `赢睿科技官网栏目文案/` 目录中的内容
- 保持专业的保险科技术语使用
- 突出 AI 技术优势和应用场景
- 强调数据驱动的价值主张

## 品牌语调

- **专业权威**: 体现保险科技的专业性
- **创新前瞻**: 强调 AI 技术的先进性
- **客户导向**: 以解决客户痛点为核心
- **数据驱动**: 用具体数据支撑价值主张
