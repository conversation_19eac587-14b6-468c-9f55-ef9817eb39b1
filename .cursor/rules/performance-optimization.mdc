---
description: 性能优化最佳实践，专注于网站速度和用户体验优化
globs: ["**/*.vue", "**/*.ts", "**/nuxt.config.ts"]
alwaysApply: false
---

# 性能优化规范

## 核心 Web Vitals 优化

### Largest Contentful Paint (LCP) 优化

```vue
<template>
  <div class="hero-section">
    <!-- 优先加载关键图像 -->
    <NuxtImg
      src="/assets/img/hero-bg.jpg"
      alt="AI保险科技背景"
      :width="1200"
      :height="600"
      format="webp"
      loading="eager"
      fetchpriority="high"
      class="hero-background"
    />

    <!-- 关键内容优先渲染 -->
    <div class="hero-content">
      <h1 class="hero-title">
        <span class="text-primary-500">AI</span> 赋能保险科技
      </h1>
    </div>
  </div>
</template>

<script setup lang="ts">
// 预加载关键资源
useHead({
  link: [
    {
      rel: "preload",
      href: "/assets/img/hero-bg.webp",
      as: "image",
      type: "image/webp",
    },
    {
      rel: "preload",
      href: "/assets/fonts/inter-var.woff2",
      as: "font",
      type: "font/woff2",
      crossorigin: "anonymous",
    },
  ],
});
</script>
```

### First Input Delay (FID) 优化

```vue
<script setup lang="ts">
// 使用 Web Workers 处理重型计算
const useWebWorker = () => {
  const worker = ref<Worker>();

  const initWorker = () => {
    if (typeof Worker !== "undefined") {
      worker.value = new Worker("/workers/data-processing.js");
    }
  };

  const processData = (data: any) => {
    return new Promise((resolve, reject) => {
      if (!worker.value) {
        initWorker();
      }

      worker.value?.postMessage(data);
      worker.value?.onmessage = (e) => resolve(e.data);
      worker.value?.onerror = (e) => reject(e);
    });
  };

  onUnmounted(() => {
    worker.value?.terminate();
  });

  return { processData };
};

// 防抖处理用户输入
const useDebounce = (fn: Function, delay: number) => {
  let timeoutId: NodeJS.Timeout;

  return (...args: any[]) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => fn.apply(null, args), delay);
  };
};

// 优化事件处理
const handleSearch = useDebounce((query: string) => {
  // 搜索逻辑
  performSearch(query);
}, 300);

// 使用 requestIdleCallback 处理非关键任务
const scheduleIdleTask = (task: Function) => {
  if ("requestIdleCallback" in window) {
    requestIdleCallback(task);
  } else {
    setTimeout(task, 0);
  }
};

// 分片处理大量数据
const processLargeDataset = (data: any[], chunkSize = 100) => {
  const chunks = [];
  for (let i = 0; i < data.length; i += chunkSize) {
    chunks.push(data.slice(i, i + chunkSize));
  }

  const processChunk = (index: number) => {
    if (index < chunks.length) {
      // 处理当前块
      processChunk(chunks[index]);

      // 调度下一块处理
      scheduleIdleTask(() => processChunk(index + 1));
    }
  };

  processChunk(0);
};
</script>
```

### Cumulative Layout Shift (CLS) 优化

```vue
<template>
  <div class="content-container">
    <!-- 使用固定尺寸容器防止布局偏移 -->
    <div class="image-container" :style="{ aspectRatio: '16/9' }">
      <NuxtImg
        src="/assets/img/product.jpg"
        alt="产品图片"
        class="responsive-image"
        @load="handleImageLoad"
      />
    </div>

    <!-- 为动态内容预留空间 -->
    <div class="dynamic-content" :class="{ 'content-loaded': contentLoaded }">
      <div v-if="!contentLoaded" class="content-skeleton">
        <div class="skeleton-line"></div>
        <div class="skeleton-line"></div>
        <div class="skeleton-line short"></div>
      </div>
      <div v-else class="actual-content">
        {{ dynamicContent }}
      </div>
    </div>
  </div>
</template>

<style scoped>
.image-container {
  @apply w-full overflow-hidden rounded-lg;
}

.responsive-image {
  @apply w-full h-full object-cover;
}

.dynamic-content {
  @apply min-h-[120px] transition-all duration-300;
}

.content-skeleton {
  @apply space-y-3 animate-pulse;
}

.skeleton-line {
  @apply h-4 bg-gray-200 rounded;
}

.skeleton-line.short {
  @apply w-3/4;
}
</style>
```

## 资源优化策略

### 图像优化

```vue
<template>
  <div class="image-gallery">
    <!-- 响应式图像 -->
    <NuxtPicture
      src="/assets/img/hero-bg.jpg"
      alt="AI保险科技"
      :img-attrs="{
        class: 'w-full h-auto',
        loading: 'lazy',
        decoding: 'async',
      }"
      sizes="xs:100vw sm:100vw md:100vw lg:100vw xl:100vw"
      format="webp,avif,jpg"
    />

    <!-- 懒加载图像组件 -->
    <LazyImage
      v-for="image in images"
      :key="image.id"
      :src="image.src"
      :alt="image.alt"
      :width="image.width"
      :height="image.height"
      class="gallery-image"
    />
  </div>
</template>

<script setup lang="ts">
// 图像预加载策略
const preloadCriticalImages = () => {
  const criticalImages = ["/assets/img/hero-bg.webp", "/assets/img/logo.webp"];

  criticalImages.forEach((src) => {
    const link = document.createElement("link");
    link.rel = "preload";
    link.as = "image";
    link.href = src;
    document.head.appendChild(link);
  });
};

// 图像压缩和格式优化
const optimizeImages = () => {
  // 在构建时自动处理
  // 支持 WebP, AVIF 格式
  // 自动生成多种尺寸
};

onMounted(() => {
  preloadCriticalImages();
});
</script>
```

### 代码分割和懒加载

```vue
<script setup lang="ts">
// 路由级别代码分割
const AsyncProductPage = defineAsyncComponent(
  () => import("~/pages/products/[slug].vue")
);

// 组件级别懒加载
const HeavyComponent = defineAsyncComponent({
  loader: () => import("~/components/HeavyComponent.vue"),
  loadingComponent: () => h("div", { class: "loading-spinner" }, "加载中..."),
  errorComponent: () => h("div", { class: "error-message" }, "加载失败"),
  delay: 200,
  timeout: 3000,
});

// 条件导入
const loadChartLibrary = async () => {
  if (showChart.value) {
    const { Chart } = await import("chart.js");
    return Chart;
  }
};

// 动态导入工具函数
const loadUtilities = async () => {
  const { debounce, throttle } = await import("lodash-es");
  return { debounce, throttle };
};

// 预加载关键路由
const preloadRoutes = () => {
  const router = useRouter();
  const criticalRoutes = ["/products", "/solutions", "/about"];

  criticalRoutes.forEach((route) => {
    router.prefetch(route);
  });
};

onMounted(() => {
  // 空闲时预加载
  if ("requestIdleCallback" in window) {
    requestIdleCallback(preloadRoutes);
  } else {
    setTimeout(preloadRoutes, 2000);
  }
});
</script>
```

### 缓存策略

```typescript
// 服务端缓存配置
export default defineNuxtConfig({
  nitro: {
    routeRules: {
      // 首页预渲染
      "/": { prerender: true },

      // 产品页面 ISR
      "/products/**": {
        isr: {
          maxAge: 60 * 60 * 24, // 24小时
          staleWhileRevalidate: 60 * 60 * 24 * 7, // 7天
        },
      },

      // API 路由缓存
      "/api/**": {
        cors: true,
        headers: {
          "Cache-Control": "public, max-age=300", // 5分钟
        },
      },

      // 静态资源长期缓存
      "/assets/**": {
        headers: {
          "Cache-Control": "public, max-age=31536000", // 1年
        },
      },
    },
  },
});

// 客户端缓存策略
const useCachedFetch = () => {
  const cache = new Map();

  const cachedFetch = async (url: string, options: any = {}) => {
    const cacheKey = `${url}${JSON.stringify(options)}`;

    // 检查缓存
    if (cache.has(cacheKey)) {
      const cached = cache.get(cacheKey);
      const now = Date.now();

      // 缓存未过期
      if (now - cached.timestamp < (options.maxAge || 300000)) {
        return cached.data;
      }
    }

    // 获取新数据
    const data = await $fetch(url, options);

    // 存储到缓存
    cache.set(cacheKey, {
      data,
      timestamp: Date.now(),
    });

    return data;
  };

  return { cachedFetch };
};
```

## 运行时性能优化

### 虚拟滚动实现

```vue
<template>
  <div class="virtual-scroll-container" @scroll="handleScroll">
    <div :style="{ height: totalHeight + 'px' }" class="virtual-content">
      <div
        v-for="item in visibleItems"
        :key="item.id"
        :style="{
          position: 'absolute',
          top: item.top + 'px',
          height: itemHeight + 'px',
          width: '100%',
        }"
        class="virtual-item"
      >
        <slot :item="item" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface VirtualScrollProps {
  items: any[];
  itemHeight: number;
  containerHeight: number;
  overscan?: number;
}

const props = withDefaults(defineProps<VirtualScrollProps>(), {
  overscan: 5,
});

const scrollTop = ref(0);
const containerRef = ref<HTMLElement>();

const totalHeight = computed(() => props.items.length * props.itemHeight);

const visibleItems = computed(() => {
  const startIndex = Math.floor(scrollTop.value / props.itemHeight);
  const endIndex = Math.min(
    startIndex +
      Math.ceil(props.containerHeight / props.itemHeight) +
      props.overscan,
    props.items.length
  );

  return props.items.slice(startIndex, endIndex).map((item, index) => ({
    ...item,
    top: (startIndex + index) * props.itemHeight,
  }));
});

const handleScroll = (event: Event) => {
  const target = event.target as HTMLElement;
  scrollTop.value = target.scrollTop;
};
</script>
```

### 内存泄漏防护

```vue
<script setup lang="ts">
// 清理定时器
const timers = ref<NodeJS.Timeout[]>([]);

const setManagedTimeout = (callback: Function, delay: number) => {
  const timer = setTimeout(() => {
    callback();
    // 从数组中移除已执行的定时器
    timers.value = timers.value.filter((t) => t !== timer);
  }, delay);

  timers.value.push(timer);
  return timer;
};

// 清理事件监听器
const eventListeners = ref<
  Array<{
    element: HTMLElement;
    event: string;
    handler: EventListener;
  }>
>([]);

const addManagedEventListener = (
  element: HTMLElement,
  event: string,
  handler: EventListener
) => {
  element.addEventListener(event, handler);
  eventListeners.value.push({ element, event, handler });
};

// 清理 DOM 引用
const domRefs = ref<HTMLElement[]>([]);

const addDomRef = (element: HTMLElement) => {
  domRefs.value.push(element);
};

// 组件卸载时清理资源
onUnmounted(() => {
  // 清理定时器
  timers.value.forEach((timer) => clearTimeout(timer));
  timers.value = [];

  // 清理事件监听器
  eventListeners.value.forEach(({ element, event, handler }) => {
    element.removeEventListener(event, handler);
  });
  eventListeners.value = [];

  // 清理 DOM 引用
  domRefs.value = [];
});

// 使用 WeakMap 存储对象关联数据
const objectData = new WeakMap();

const setObjectData = (obj: object, data: any) => {
  objectData.set(obj, data);
};

const getObjectData = (obj: object) => {
  return objectData.get(obj);
};
</script>
```

### 性能监控

```typescript
// 性能监控工具
class PerformanceMonitor {
  private metrics: Map<string, number[]> = new Map();

  // 测量函数执行时间
  measureFunction<T>(name: string, fn: () => T): T {
    const start = performance.now();
    const result = fn();
    const end = performance.now();

    this.recordMetric(name, end - start);
    return result;
  }

  // 测量异步函数执行时间
  async measureAsync<T>(name: string, fn: () => Promise<T>): Promise<T> {
    const start = performance.now();
    const result = await fn();
    const end = performance.now();

    this.recordMetric(name, end - start);
    return result;
  }

  // 记录指标
  private recordMetric(name: string, value: number) {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    this.metrics.get(name)!.push(value);
  }

  // 获取性能报告
  getReport() {
    const report: Record<string, any> = {};

    this.metrics.forEach((values, name) => {
      const sorted = values.sort((a, b) => a - b);
      const len = sorted.length;

      report[name] = {
        count: len,
        min: sorted[0],
        max: sorted[len - 1],
        avg: sorted.reduce((a, b) => a + b, 0) / len,
        median:
          len % 2 === 0
            ? (sorted[len / 2 - 1] + sorted[len / 2]) / 2
            : sorted[Math.floor(len / 2)],
        p95: sorted[Math.floor(len * 0.95)],
      };
    });

    return report;
  }

  // 监控 Core Web Vitals
  observeWebVitals() {
    // LCP 监控
    const lcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      this.recordMetric("LCP", lastEntry.startTime);
    });

    lcpObserver.observe({ entryTypes: ["largest-contentful-paint"] });

    // FID 监控
    const fidObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry) => {
        this.recordMetric("FID", entry.processingStart - entry.startTime);
      });
    });

    fidObserver.observe({ entryTypes: ["first-input"] });

    // CLS 监控
    let clsValue = 0;
    const clsObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry) => {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
        }
      });
      this.recordMetric("CLS", clsValue);
    });

    clsObserver.observe({ entryTypes: ["layout-shift"] });
  }
}

// 全局性能监控实例
const performanceMonitor = new PerformanceMonitor();

// 在应用中使用
export const usePerformanceMonitoring = () => {
  onMounted(() => {
    performanceMonitor.observeWebVitals();
  });

  const measurePageLoad = (pageName: string) => {
    return performanceMonitor.measureAsync(
      `page-load-${pageName}`,
      () =>
        new Promise((resolve) => {
          const observer = new PerformanceObserver((list) => {
            const entry = list.getEntries()[0];
            observer.disconnect();
            resolve(entry);
          });
          observer.observe({ entryTypes: ["navigation"] });
        })
    );
  };

  return {
    measureFunction:
      performanceMonitor.measureFunction.bind(performanceMonitor),
    measureAsync: performanceMonitor.measureAsync.bind(performanceMonitor),
    getReport: performanceMonitor.getReport.bind(performanceMonitor),
    measurePageLoad,
  };
};
```

## 构建优化

### Webpack/Vite 优化配置

```typescript
// nuxt.config.ts 优化配置
export default defineNuxtConfig({
  // 构建优化
  build: {
    optimization: {
      splitChunks: {
        chunks: "all",
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: "vendors",
            chunks: "all",
          },
          common: {
            name: "common",
            minChunks: 2,
            chunks: "all",
            enforce: true,
          },
        },
      },
    },
  },

  // Vite 配置
  vite: {
    build: {
      rollupOptions: {
        output: {
          manualChunks: {
            "vue-vendor": ["vue", "vue-router"],
            "ui-vendor": ["@nuxt/ui"],
            utils: ["lodash-es", "date-fns"],
          },
        },
      },
    },
  },

  // 实验性功能
  experimental: {
    payloadExtraction: false, // 禁用 payload 提取以减少包大小
    inlineSSRStyles: false, // 内联关键 CSS
  },

  // 图像优化
  image: {
    formats: ["webp", "avif"],
    quality: 80,
    densities: [1, 2],
    sizes: "xs:100vw sm:100vw md:100vw lg:100vw xl:100vw",
  },
});
```
