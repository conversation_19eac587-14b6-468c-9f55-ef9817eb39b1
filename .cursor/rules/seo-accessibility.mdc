---
description: SEO 优化和可访问性规范，确保网站搜索引擎友好和无障碍访问
globs: ["**/*.vue", "**/*.ts"]
alwaysApply: false
---

# SEO 优化和可访问性规范

## SEO 基础配置

### 页面元数据优化

```vue
<script setup lang="ts">
// 页面 SEO 配置接口
interface SEOConfig {
  title: string;
  description: string;
  keywords?: string[];
  ogImage?: string;
  canonicalUrl?: string;
  structuredData?: any;
}

// AI 风控定价系统页面 SEO 示例
const seoConfig: SEOConfig = {
  title: "AI风控定价系统 - 智能保险科技解决方案",
  description:
    "基于AI大语言模型的保险风控定价系统，实现分钟级部署、高精度定价和长尾市场拓展。风险识别准确率95%，助力保险公司数字化转型。",
  keywords: [
    "AI风控定价",
    "保险科技",
    "智能核保",
    "风险评估",
    "保险AI解决方案",
    "数字化转型",
  ],
  ogImage: "/assets/img/ai-risk-pricing-og.jpg",
  canonicalUrl: "https://www.yingrtech.com/products/ai-risk-pricing",
};

// 使用 useHead 配置页面元数据
useHead({
  title: seoConfig.title,
  meta: [
    // 基础 meta 标签
    { name: "description", content: seoConfig.description },
    { name: "keywords", content: seoConfig.keywords?.join(", ") },
    { name: "author", content: "赢睿保险科技" },
    { name: "robots", content: "index, follow" },

    // Open Graph 标签
    { property: "og:title", content: seoConfig.title },
    { property: "og:description", content: seoConfig.description },
    { property: "og:type", content: "website" },
    { property: "og:url", content: seoConfig.canonicalUrl },
    { property: "og:image", content: seoConfig.ogImage },
    { property: "og:site_name", content: "赢睿保险科技" },
    { property: "og:locale", content: "zh_CN" },

    // Twitter Card 标签
    { name: "twitter:card", content: "summary_large_image" },
    { name: "twitter:title", content: seoConfig.title },
    { name: "twitter:description", content: seoConfig.description },
    { name: "twitter:image", content: seoConfig.ogImage },

    // 移动端优化
    { name: "viewport", content: "width=device-width, initial-scale=1" },
    { name: "format-detection", content: "telephone=no" },

    // 搜索引擎验证
    { name: "baidu-site-verification", content: "verification-code" },
    { name: "google-site-verification", content: "verification-code" },
  ],

  link: [
    // 规范链接
    { rel: "canonical", href: seoConfig.canonicalUrl },

    // DNS 预解析
    { rel: "dns-prefetch", href: "//fonts.googleapis.com" },
    { rel: "dns-prefetch", href: "//www.google-analytics.com" },

    // 资源预加载
    {
      rel: "preload",
      href: "/assets/fonts/inter-var.woff2",
      as: "font",
      type: "font/woff2",
      crossorigin: "anonymous",
    },
  ],
});

// 结构化数据配置
const structuredData = {
  "@context": "https://schema.org",
  "@type": "SoftwareApplication",
  name: "AI风控定价系统",
  description: seoConfig.description,
  applicationCategory: "BusinessApplication",
  operatingSystem: "Web-based",
  url: seoConfig.canonicalUrl,
  image: seoConfig.ogImage,
  provider: {
    "@type": "Organization",
    name: "赢睿保险科技",
    url: "https://www.yingrtech.com",
    logo: "https://www.yingrtech.com/assets/img/logo.png",
    contactPoint: {
      "@type": "ContactPoint",
      telephone: "+86 181 2137 8388",
      contactType: "customer service",
      availableLanguage: "Chinese",
    },
  },
  offers: {
    "@type": "Offer",
    priceCurrency: "CNY",
    price: "0",
    description: "联系获取定制化报价",
  },
  aggregateRating: {
    "@type": "AggregateRating",
    ratingValue: "4.8",
    reviewCount: "156",
  },
};

// 注入结构化数据
useHead({
  script: [
    {
      type: "application/ld+json",
      innerHTML: JSON.stringify(structuredData),
    },
  ],
});
</script>
```

### 语义化 HTML 结构

```vue
<template>
  <div itemscope itemtype="https://schema.org/WebPage">
    <!-- 页面头部 -->
    <header role="banner" class="site-header">
      <nav role="navigation" aria-label="主导航">
        <ul class="nav-list">
          <li><a href="/" aria-current="page">首页</a></li>
          <li><a href="/products">产品体系</a></li>
          <li><a href="/solutions">解决方案</a></li>
        </ul>
      </nav>
    </header>

    <!-- 主要内容区域 -->
    <main role="main" id="main-content">
      <!-- 面包屑导航 -->
      <nav aria-label="面包屑导航" class="breadcrumb">
        <ol itemscope itemtype="https://schema.org/BreadcrumbList">
          <li
            itemprop="itemListElement"
            itemscope
            itemtype="https://schema.org/ListItem"
          >
            <a itemprop="item" href="/"><span itemprop="name">首页</span></a>
            <meta itemprop="position" content="1" />
          </li>
          <li
            itemprop="itemListElement"
            itemscope
            itemtype="https://schema.org/ListItem"
          >
            <a itemprop="item" href="/products"
              ><span itemprop="name">产品体系</span></a
            >
            <meta itemprop="position" content="2" />
          </li>
          <li
            itemprop="itemListElement"
            itemscope
            itemtype="https://schema.org/ListItem"
          >
            <span itemprop="name">AI风控定价系统</span>
            <meta itemprop="position" content="3" />
          </li>
        </ol>
      </nav>

      <!-- 页面内容 -->
      <article itemscope itemtype="https://schema.org/TechArticle">
        <header class="article-header">
          <h1 itemprop="headline">AI风控定价系统</h1>
          <p itemprop="description" class="article-description">
            基于AI大语言模型的智能保险风控定价解决方案
          </p>

          <!-- 文章元信息 -->
          <div class="article-meta">
            <time itemprop="datePublished" datetime="2024-01-01"
              >2024年1月1日</time
            >
            <span
              itemprop="author"
              itemscope
              itemtype="https://schema.org/Organization"
            >
              <span itemprop="name">赢睿保险科技</span>
            </span>
          </div>
        </header>

        <!-- 文章内容 -->
        <div itemprop="articleBody" class="article-content">
          <section aria-labelledby="core-features">
            <h2 id="core-features">核心功能特性</h2>
            <ul>
              <li>AI大语言模型驱动的风险评估</li>
              <li>实时动态定价引擎</li>
              <li>智能合规规则检查</li>
            </ul>
          </section>

          <section aria-labelledby="application-scenarios">
            <h2 id="application-scenarios">应用场景</h2>
            <p>适用于货运险、车险、健康险等多种保险产品的风控定价...</p>
          </section>
        </div>
      </article>
    </main>

    <!-- 页面侧边栏 -->
    <aside role="complementary" aria-label="相关信息">
      <section aria-labelledby="related-products">
        <h3 id="related-products">相关产品</h3>
        <ul>
          <li><a href="/products/ai-claims">AI理赔管理系统</a></li>
          <li><a href="/products/ai-customer-service">AI客服系统</a></li>
        </ul>
      </section>
    </aside>

    <!-- 页面底部 -->
    <footer role="contentinfo" class="site-footer">
      <div class="footer-content">
        <p>&copy; 2024 赢睿保险科技. 保留所有权利.</p>
      </div>
    </footer>
  </div>
</template>
```

## 可访问性 (A11y) 规范

### 键盘导航支持

```vue
<template>
  <div class="accessible-navigation">
    <!-- 跳过链接 -->
    <a href="#main-content" class="skip-link">跳转到主内容</a>

    <!-- 可访问的菜单 -->
    <nav role="navigation" aria-label="主导航">
      <button
        ref="menuToggle"
        :aria-expanded="isMenuOpen"
        :aria-controls="menuId"
        aria-label="切换导航菜单"
        class="menu-toggle"
        @click="toggleMenu"
        @keydown="handleMenuToggleKeydown"
      >
        <span class="sr-only">{{ isMenuOpen ? "关闭" : "打开" }}菜单</span>
        <MenuIcon :isOpen="isMenuOpen" />
      </button>

      <ul
        :id="menuId"
        ref="menuList"
        class="menu-list"
        :class="{ 'menu-open': isMenuOpen }"
        role="menubar"
        :aria-hidden="!isMenuOpen"
      >
        <li v-for="(item, index) in menuItems" :key="item.id" role="none">
          <a
            :href="item.href"
            role="menuitem"
            :tabindex="isMenuOpen ? 0 : -1"
            :aria-current="isCurrentPage(item.href) ? 'page' : undefined"
            class="menu-link"
            @keydown="handleMenuItemKeydown($event, index)"
            @focus="handleMenuItemFocus(index)"
          >
            {{ item.name }}
          </a>
        </li>
      </ul>
    </nav>
  </div>
</template>

<script setup lang="ts">
const isMenuOpen = ref(false);
const menuId = "main-navigation";
const menuToggle = ref<HTMLElement>();
const menuList = ref<HTMLElement>();
const currentFocusIndex = ref(0);

const menuItems = [
  { id: "home", name: "首页", href: "/" },
  { id: "products", name: "产品体系", href: "/products" },
  { id: "solutions", name: "解决方案", href: "/solutions" },
  { id: "about", name: "关于我们", href: "/about" },
];

// 切换菜单状态
const toggleMenu = () => {
  isMenuOpen.value = !isMenuOpen.value;

  if (isMenuOpen.value) {
    // 菜单打开时，焦点移到第一个菜单项
    nextTick(() => {
      const firstMenuItem = menuList.value?.querySelector(
        '[role="menuitem"]'
      ) as HTMLElement;
      firstMenuItem?.focus();
    });
  }
};

// 处理菜单切换按钮键盘事件
const handleMenuToggleKeydown = (event: KeyboardEvent) => {
  switch (event.key) {
    case "Enter":
    case " ":
      event.preventDefault();
      toggleMenu();
      break;
    case "Escape":
      if (isMenuOpen.value) {
        isMenuOpen.value = false;
        menuToggle.value?.focus();
      }
      break;
  }
};

// 处理菜单项键盘导航
const handleMenuItemKeydown = (event: KeyboardEvent, index: number) => {
  switch (event.key) {
    case "ArrowDown":
      event.preventDefault();
      focusMenuItem(index + 1);
      break;
    case "ArrowUp":
      event.preventDefault();
      focusMenuItem(index - 1);
      break;
    case "Home":
      event.preventDefault();
      focusMenuItem(0);
      break;
    case "End":
      event.preventDefault();
      focusMenuItem(menuItems.length - 1);
      break;
    case "Escape":
      isMenuOpen.value = false;
      menuToggle.value?.focus();
      break;
    case "Tab":
      // 允许 Tab 键正常行为，但在最后一项时关闭菜单
      if (index === menuItems.length - 1 && !event.shiftKey) {
        isMenuOpen.value = false;
      }
      break;
  }
};

// 聚焦到指定菜单项
const focusMenuItem = (index: number) => {
  const clampedIndex = Math.max(0, Math.min(index, menuItems.length - 1));
  const menuItems = menuList.value?.querySelectorAll('[role="menuitem"]');
  const targetItem = menuItems?.[clampedIndex] as HTMLElement;
  targetItem?.focus();
  currentFocusIndex.value = clampedIndex;
};

// 处理菜单项获得焦点
const handleMenuItemFocus = (index: number) => {
  currentFocusIndex.value = index;
};

// 检查当前页面
const isCurrentPage = (href: string) => {
  return useRoute().path === href;
};

// 点击外部关闭菜单
const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  if (isMenuOpen.value && !target.closest(".accessible-navigation")) {
    isMenuOpen.value = false;
  }
};

onMounted(() => {
  document.addEventListener("click", handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener("click", handleClickOutside);
});
</script>

<style scoped>
.skip-link {
  @apply absolute -top-full left-4 z-50
         bg-primary-500 text-white px-4 py-2 rounded
         focus:top-4 transition-all duration-200;
}

.menu-toggle {
  @apply p-2 rounded-lg border-2 border-transparent
         focus:border-primary-500 focus:outline-none
         hover:bg-gray-100 transition-colors;
}

.menu-list {
  @apply hidden flex-col space-y-2 mt-4
         lg:flex lg:flex-row lg:space-y-0 lg:space-x-4 lg:mt-0;
}

.menu-list.menu-open {
  @apply flex;
}

.menu-link {
  @apply block px-4 py-2 rounded-lg text-gray-700
         hover:bg-primary-50 hover:text-primary-700
         focus:bg-primary-100 focus:text-primary-800 focus:outline-none
         transition-colors;
}

.menu-link[aria-current="page"] {
  @apply bg-primary-500 text-white;
}

.sr-only {
  @apply absolute w-px h-px p-0 -m-px overflow-hidden
         whitespace-nowrap border-0;
  clip: rect(0, 0, 0, 0);
}
</style>
```

### 表单可访问性

```vue
<template>
  <form
    class="accessible-form"
    @submit="handleSubmit"
    novalidate
    aria-label="产品咨询表单"
  >
    <!-- 表单标题 -->
    <fieldset>
      <legend class="form-legend">联系信息</legend>

      <!-- 姓名字段 -->
      <div class="form-group">
        <label for="name" class="form-label">
          姓名
          <span class="required-indicator" aria-label="必填">*</span>
        </label>
        <input
          id="name"
          v-model="form.name"
          type="text"
          class="form-input"
          :class="{ 'form-input--error': errors.name }"
          :aria-describedby="errors.name ? 'name-error' : 'name-help'"
          :aria-invalid="errors.name ? 'true' : 'false'"
          required
          autocomplete="name"
        />
        <div id="name-help" class="form-help">请输入您的真实姓名</div>
        <div
          v-if="errors.name"
          id="name-error"
          class="form-error"
          role="alert"
          aria-live="polite"
        >
          <UIcon name="i-heroicons-exclamation-circle" class="error-icon" />
          {{ errors.name }}
        </div>
      </div>

      <!-- 邮箱字段 -->
      <div class="form-group">
        <label for="email" class="form-label">
          邮箱地址
          <span class="required-indicator" aria-label="必填">*</span>
        </label>
        <input
          id="email"
          v-model="form.email"
          type="email"
          class="form-input"
          :class="{ 'form-input--error': errors.email }"
          :aria-describedby="errors.email ? 'email-error' : 'email-help'"
          :aria-invalid="errors.email ? 'true' : 'false'"
          required
          autocomplete="email"
        />
        <div id="email-help" class="form-help">我们将通过邮箱与您联系</div>
        <div
          v-if="errors.email"
          id="email-error"
          class="form-error"
          role="alert"
          aria-live="polite"
        >
          <UIcon name="i-heroicons-exclamation-circle" class="error-icon" />
          {{ errors.email }}
        </div>
      </div>

      <!-- 电话字段 -->
      <div class="form-group">
        <label for="phone" class="form-label"> 联系电话 </label>
        <input
          id="phone"
          v-model="form.phone"
          type="tel"
          class="form-input"
          :class="{ 'form-input--error': errors.phone }"
          :aria-describedby="errors.phone ? 'phone-error' : 'phone-help'"
          :aria-invalid="errors.phone ? 'true' : 'false'"
          autocomplete="tel"
          pattern="[0-9]{11}"
        />
        <div id="phone-help" class="form-help">请输入11位手机号码（可选）</div>
        <div
          v-if="errors.phone"
          id="phone-error"
          class="form-error"
          role="alert"
          aria-live="polite"
        >
          <UIcon name="i-heroicons-exclamation-circle" class="error-icon" />
          {{ errors.phone }}
        </div>
      </div>
    </fieldset>

    <!-- 咨询内容 -->
    <fieldset>
      <legend class="form-legend">咨询内容</legend>

      <!-- 产品选择 -->
      <div class="form-group">
        <fieldset>
          <legend class="form-sublabel">感兴趣的产品（可多选）</legend>
          <div
            class="checkbox-group"
            role="group"
            aria-labelledby="products-legend"
          >
            <div
              v-for="product in products"
              :key="product.id"
              class="checkbox-item"
            >
              <input
                :id="`product-${product.id}`"
                v-model="form.products"
                type="checkbox"
                :value="product.id"
                class="form-checkbox"
              />
              <label :for="`product-${product.id}`" class="checkbox-label">
                {{ product.name }}
              </label>
            </div>
          </div>
        </fieldset>
      </div>

      <!-- 消息内容 -->
      <div class="form-group">
        <label for="message" class="form-label">
          详细需求
          <span class="required-indicator" aria-label="必填">*</span>
        </label>
        <textarea
          id="message"
          v-model="form.message"
          class="form-textarea"
          :class="{ 'form-input--error': errors.message }"
          :aria-describedby="errors.message ? 'message-error' : 'message-help'"
          :aria-invalid="errors.message ? 'true' : 'false'"
          rows="4"
          required
          maxlength="500"
        ></textarea>
        <div id="message-help" class="form-help">
          请详细描述您的需求（最多500字）
        </div>
        <div class="character-count" aria-live="polite">
          {{ form.message.length }}/500
        </div>
        <div
          v-if="errors.message"
          id="message-error"
          class="form-error"
          role="alert"
          aria-live="polite"
        >
          <UIcon name="i-heroicons-exclamation-circle" class="error-icon" />
          {{ errors.message }}
        </div>
      </div>
    </fieldset>

    <!-- 提交按钮 -->
    <div class="form-actions">
      <button
        type="submit"
        class="form-submit"
        :disabled="isSubmitting"
        :aria-describedby="isSubmitting ? 'submit-status' : undefined"
      >
        <span v-if="!isSubmitting">提交咨询</span>
        <span v-else>
          <UIcon name="i-heroicons-arrow-path" class="animate-spin mr-2" />
          提交中...
        </span>
      </button>

      <div
        v-if="isSubmitting"
        id="submit-status"
        class="sr-only"
        aria-live="polite"
      >
        正在提交表单，请稍候
      </div>
    </div>

    <!-- 成功消息 -->
    <div
      v-if="submitSuccess"
      class="form-success"
      role="alert"
      aria-live="polite"
    >
      <UIcon name="i-heroicons-check-circle" class="success-icon" />
      感谢您的咨询！我们将在24小时内与您联系。
    </div>
  </form>
</template>

<script setup lang="ts">
interface ContactForm {
  name: string;
  email: string;
  phone: string;
  products: string[];
  message: string;
}

const form = reactive<ContactForm>({
  name: "",
  email: "",
  phone: "",
  products: [],
  message: "",
});

const errors = reactive<Partial<ContactForm>>({});
const isSubmitting = ref(false);
const submitSuccess = ref(false);

const products = [
  { id: "ai-risk-pricing", name: "AI风控定价系统" },
  { id: "ai-claims", name: "AI理赔管理系统" },
  { id: "ai-customer-service", name: "AI客服系统" },
  { id: "ai-knowledge-base", name: "AI企业知识库" },
];

// 表单验证
const validateForm = (): boolean => {
  // 清空之前的错误
  Object.keys(errors).forEach((key) => delete errors[key]);

  if (!form.name.trim()) {
    errors.name = "请输入姓名";
  }

  if (!form.email.trim()) {
    errors.email = "请输入邮箱地址";
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email)) {
    errors.email = "请输入有效的邮箱地址";
  }

  if (form.phone && !/^1[3-9]\d{9}$/.test(form.phone)) {
    errors.phone = "请输入有效的手机号码";
  }

  if (!form.message.trim()) {
    errors.message = "请输入详细需求";
  } else if (form.message.length > 500) {
    errors.message = "内容不能超过500字";
  }

  return Object.keys(errors).length === 0;
};

// 处理表单提交
const handleSubmit = async (event: Event) => {
  event.preventDefault();

  if (!validateForm()) {
    // 聚焦到第一个错误字段
    const firstErrorField = Object.keys(errors)[0];
    const errorElement = document.getElementById(firstErrorField);
    errorElement?.focus();
    return;
  }

  try {
    isSubmitting.value = true;

    // 提交表单数据
    await submitContactForm(form);

    submitSuccess.value = true;

    // 重置表单
    Object.keys(form).forEach((key) => {
      if (Array.isArray(form[key])) {
        form[key] = [];
      } else {
        form[key] = "";
      }
    });
  } catch (error) {
    console.error("表单提交失败:", error);
    // 显示错误消息
  } finally {
    isSubmitting.value = false;
  }
};
</script>

<style scoped>
.accessible-form {
  @apply max-w-2xl mx-auto space-y-8;
}

.form-legend {
  @apply text-lg font-semibold text-gray-900 mb-6
         border-b border-gray-200 pb-2;
}

.form-sublabel {
  @apply text-base font-medium text-gray-700 mb-4;
}

.form-group {
  @apply space-y-2;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

.required-indicator {
  @apply text-red-500 ml-1;
}

.form-input,
.form-textarea {
  @apply w-full px-4 py-3 border border-gray-300 rounded-lg
         focus:ring-2 focus:ring-primary-500 focus:border-primary-500
         transition-colors duration-200
         placeholder:text-gray-400;
}

.form-input--error {
  @apply border-red-500 focus:ring-red-500 focus:border-red-500;
}

.form-help {
  @apply text-sm text-gray-600;
}

.form-error {
  @apply text-sm text-red-600 mt-1 flex items-center space-x-1;
}

.error-icon {
  @apply w-4 h-4 flex-shrink-0;
}

.checkbox-group {
  @apply space-y-3;
}

.checkbox-item {
  @apply flex items-center space-x-3;
}

.form-checkbox {
  @apply w-4 h-4 text-primary-600 border-gray-300 rounded
         focus:ring-primary-500;
}

.checkbox-label {
  @apply text-sm text-gray-700 cursor-pointer;
}

.character-count {
  @apply text-xs text-gray-500 text-right mt-1;
}

.form-actions {
  @apply pt-6;
}

.form-submit {
  @apply w-full bg-primary-500 hover:bg-primary-600 
         disabled:bg-gray-400 disabled:cursor-not-allowed
         text-white font-semibold py-3 px-6 rounded-lg
         transition-colors duration-200
         focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
}

.form-success {
  @apply bg-green-50 border border-green-200 text-green-800
         p-4 rounded-lg flex items-center space-x-2;
}

.success-icon {
  @apply w-5 h-5 text-green-600 flex-shrink-0;
}
</style>
```

## 搜索引擎优化进阶

### 网站地图生成

```typescript
// 自动生成网站地图
export default defineNuxtConfig({
  modules: ["@nuxtjs/sitemap"],

  sitemap: {
    hostname: "https://www.yingrtech.com",
    gzip: true,
    routes: async () => {
      // 动态路由
      const products = await fetchProducts();
      const solutions = await fetchSolutions();

      const routes = [
        // 静态页面
        "/",
        "/about",
        "/contact",

        // 产品页面
        ...products.map((product) => `/products/${product.slug}`),

        // 解决方案页面
        ...solutions.map((solution) => `/solutions/${solution.slug}`),
      ];

      return routes.map((route) => ({
        url: route,
        changefreq: "weekly",
        priority: route === "/" ? 1.0 : 0.8,
        lastmod: new Date().toISOString(),
      }));
    },
  },
});

// robots.txt 配置
export default defineNuxtConfig({
  modules: ["@nuxtjs/robots"],

  robots: {
    UserAgent: "*",
    Allow: "/",
    Disallow: ["/admin", "/api/private"],
    Sitemap: "https://www.yingrtech.com/sitemap.xml",
  },
});
```

### 性能和 SEO 监控

```typescript
// SEO 性能监控
class SEOMonitor {
  // 监控核心 Web Vitals
  monitorWebVitals() {
    // LCP 监控
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];

      // 发送到分析服务
      this.sendMetric("LCP", lastEntry.startTime);
    }).observe({ entryTypes: ["largest-contentful-paint"] });

    // CLS 监控
    let clsValue = 0;
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry) => {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
        }
      });
      this.sendMetric("CLS", clsValue);
    }).observe({ entryTypes: ["layout-shift"] });
  }

  // 监控页面索引状态
  async checkIndexStatus(urls: string[]) {
    const results = [];

    for (const url of urls) {
      try {
        const response = await fetch(
          `https://www.googleapis.com/pagespeedinights/v5/runPagespeed?url=${encodeURIComponent(
            url
          )}`
        );
        const data = await response.json();

        results.push({
          url,
          indexed: data.lighthouseResult?.audits?.["is-crawlable"]?.score === 1,
          performance: data.lighthouseResult?.categories?.performance?.score,
          seo: data.lighthouseResult?.categories?.seo?.score,
          accessibility:
            data.lighthouseResult?.categories?.accessibility?.score,
        });
      } catch (error) {
        console.error(`检查 ${url} 失败:`, error);
      }
    }

    return results;
  }

  // 发送指标到分析服务
  private sendMetric(name: string, value: number) {
    // 发送到 Google Analytics 或其他分析服务
    if (typeof gtag !== "undefined") {
      gtag("event", name, {
        event_category: "Web Vitals",
        value: Math.round(value),
        non_interaction: true,
      });
    }
  }
}

// 在应用中使用
export const useSEOMonitoring = () => {
  const monitor = new SEOMonitor();

  onMounted(() => {
    monitor.monitorWebVitals();
  });

  return {
    checkIndexStatus: monitor.checkIndexStatus.bind(monitor),
  };
};
```
