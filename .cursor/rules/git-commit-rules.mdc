# Git 提交规范

## 🎯 提交信息格式

```
<类型>(<范围>): <简短描述>

<详细描述>

<页脚信息>
```

### 类型 (Type)

| 类型       | 说明          | 示例                                  |
| ---------- | ------------- | ------------------------------------- |
| `feat`     | 新功能        | `feat(首页): 添加 AI 产品展示轮播图`  |
| `fix`      | 修复 Bug      | `fix(登录): 修复手机号验证失败问题`   |
| `docs`     | 文档更新      | `docs(README): 更新项目启动说明`      |
| `style`    | 代码格式调整  | `style(组件): 统一按钮样式命名`       |
| `refactor` | 代码重构      | `refactor(API): 重构用户认证逻辑`     |
| `perf`     | 性能优化      | `perf(图片): 优化图片懒加载性能`      |
| `test`     | 测试相关      | `test(组件): 添加按钮组件单元测试`    |
| `chore`    | 构建/工具相关 | `chore(依赖): 更新 Tailwind CSS 版本` |

### 范围 (Scope)

- 可选，表示影响的功能模块
- 使用小写字母，用括号包围
- 常见范围：`首页`、`产品`、`服务`、`组件`、`API`、`样式`等

### 简短描述

- 用中文描述，简洁明了
- 不超过 50 个字符
- 以动词开头，描述做了什么

## 📝 提交示例

### ✅ 好的提交信息

```
feat(首页): 添加 AI 产品展示轮播图

- 实现自动轮播功能
- 支持触摸滑动
- 添加轮播指示器
- 优化移动端体验

Closes #123
```

```
fix(登录): 修复手机号验证失败问题

修复了手机号格式验证逻辑错误，现在可以正确验证 11 位手机号

Fixes #456
```

```
docs(README): 更新项目启动说明

添加了详细的开发环境配置步骤和常见问题解决方案
```

### ❌ 不好的提交信息

```
更新了代码
修复了问题
添加功能
```

## 🔧 提交规范检查

### 使用 Git Hooks

在项目根目录创建 `.git/hooks/commit-msg` 文件：

```bash
#!/bin/sh
# 检查提交信息格式
commit_regex='^(feat|fix|docs|style|refactor|perf|test|chore)(\(.+\))?: .{1,50}$'

if ! grep -qE "$commit_regex" "$1"; then
    echo "❌ 提交信息格式不正确！"
    echo "正确格式: <类型>(<范围>): <简短描述>"
    echo "示例: feat(首页): 添加轮播图组件"
    exit 1
fi

echo "✅ 提交信息格式正确"
```

### 使用 Commitizen

安装 Commitizen 工具：

```bash
npm install -g commitizen
npm install -g cz-conventional-changelog
```

在 `package.json` 中添加：

```json
{
  "scripts": {
    "commit": "cz"
  },
  "config": {
    "commitizen": {
      "path": "cz-conventional-changelog"
    }
  }
}
```

使用方式：

```bash
npm run commit
```

## 📋 提交检查清单

### 提交前检查

- [ ] 代码已通过测试
- [ ] 代码符合项目规范
- [ ] 提交信息格式正确
- [ ] 提交范围明确
- [ ] 描述清晰易懂

### 分支管理

- `main`: 主分支，用于生产环境
- `develop`: 开发分支，用于集成测试
- `feature/*`: 功能分支，用于开发新功能
- `hotfix/*`: 热修复分支，用于紧急修复

## 🚀 快速开始

### 1. 克隆项目后设置

```bash
# 设置用户信息
git config user.name "你的姓名"
git config user.email "你的邮箱"

# 设置默认分支
git config init.defaultBranch main
```

### 2. 日常开发流程

```bash
# 1. 创建功能分支
git checkout -b feature/新功能名称

# 2. 开发完成后提交
git add .
git commit -m "feat(功能模块): 实现新功能描述"

# 3. 推送到远程
git push origin feature/新功能名称

# 4. 创建合并请求
# 在 GitLab/GitHub 上创建 MR/PR
```

### 3. 紧急修复流程

```bash
# 1. 从 main 分支创建热修复分支
git checkout -b hotfix/紧急问题描述

# 2. 修复问题
# ... 修复代码 ...

# 3. 提交修复
git commit -m "fix(问题模块): 修复紧急问题描述"

# 4. 合并到 main 和 develop
git checkout main
git merge hotfix/紧急问题描述
git checkout develop
git merge hotfix/紧急问题描述

# 5. 删除热修复分支
git branch -d hotfix/紧急问题描述
```

## 💡 最佳实践

### 1. 提交频率

- 功能完整后再提交，避免频繁小提交
- 每个提交应该是一个完整的功能或修复
- 提交信息要能清楚说明这次提交的目的

### 2. 分支命名

- 功能分支：`feature/功能名称`
- 修复分支：`fix/问题描述`
- 热修复：`hotfix/紧急问题`
- 发布分支：`release/版本号`

### 3. 合并策略

- 使用 `--no-ff` 保留分支历史
- 合并前先进行代码审查
- 解决冲突后再次测试

## 🔍 常见问题

### Q: 提交信息写错了怎么办？

A: 使用 `git commit --amend` 修改最后一次提交信息

### Q: 如何查看提交历史？

A: 使用 `git log --oneline` 查看简洁历史，`git log --graph` 查看图形化历史

### Q: 如何撤销提交？

A: 使用 `git reset --soft HEAD~1` 撤销最后一次提交但保留更改

### Q: 分支合并冲突怎么解决？

A: 手动编辑冲突文件，解决冲突后 `git add` 和 `git commit`

## 📚 参考资源

- [Conventional Commits](https://www.conventionalcommits.org/)
- [Git 官方文档](https://git-scm.com/doc)
- [GitHub Flow](https://guides.github.com/introduction/flow/)
- [GitLab Flow](https://docs.gitlab.com/ee/topics/gitlab_flow.html)

---

**记住**: 好的提交信息是项目历史的重要组成部分，它帮助团队成员理解代码变更的原因和影响范围。保持提交信息的清晰和一致性，将大大提高项目的可维护性。
description:
globs:
alwaysApply: true

---
