---
description: 赢睿科技官网项目 Cursor Rules 使用指南和规则说明
alwaysApply: true
---

# 赢睿科技官网 Cursor Rules 使用指南

本项目的 Cursor Rules 规则文件旨在确保开发团队在构建赢睿保险科技官网时遵循最佳实践，保持代码质量和品牌一致性。

## 规则文件概览

### 📋 核心规则文件

| 文件名                         | 描述                         | 适用范围     |
| ------------------------------ | ---------------------------- | ------------ |
| `project-overview.mdc`         | 项目总体开发规范和指导原则   | 全项目       |
| `nuxt-vue-development.mdc`     | Nuxt 3 和 Vue 3 开发最佳实践 | Vue/TS 文件  |
| `component-development.mdc`    | 组件开发规范和模式           | 组件文件     |
| `content-management.mdc`       | 内容管理和文案规范           | 内容相关文件 |
| `styling-design.mdc`           | 样式和设计系统规范           | 样式相关文件 |
| `performance-optimization.mdc` | 性能优化最佳实践             | 全项目       |
| `seo-accessibility.mdc`        | SEO 和可访问性规范           | Vue/TS 文件  |

## 🚀 快速开始

### 1. 理解项目定位

- **行业定位**: AI 保险科技领域
- **目标用户**: 保险公司、中介机构、MGA、物流企业
- **核心价值**: AI 技术驱动的保险业务智能化转型

### 2. 技术栈掌握

```bash
# 主要技术栈
- Nuxt 3 + Vue 3 + TypeScript
- Tailwind CSS 4.x + @nuxt/ui
- @nuxt/image + @vueuse/motion
```

### 3. 开发工作流

1. 查看相关规则文件了解规范
2. 参考 `赢睿科技官网栏目文案/` 目录中的内容
3. 使用规范的组件模式和样式系统
4. 确保 SEO 和可访问性要求
5. 进行性能优化检查

## 📖 规则详细说明

### 项目总体规范 (`project-overview.mdc`)

**核心原则:**

- 中文优先，保持专业的保险科技形象
- AI 技术主题一致性，强调创新和专业
- 响应式设计优先，确保全设备兼容
- 性能优化，关注用户体验

**品牌语调:**

- 专业权威：体现保险科技专业性
- 创新前瞻：强调 AI 技术先进性
- 客户导向：以解决客户痛点为核心
- 数据驱动：用具体数据支撑价值主张

### Vue 开发规范 (`nuxt-vue-development.mdc`)

**组件开发要点:**

```vue
<script setup lang="ts">
// 1. 导入顺序：Vue -> Nuxt -> 第三方 -> 本地
// 2. 使用 TypeScript 严格类型检查
// 3. Props 接口定义清晰
// 4. 合理使用响应式 API

interface Props {
  title: string;
  description?: string;
}

const props = withDefaults(defineProps<Props>(), {
  description: "默认描述",
});
</script>
```

**SEO 优化:**

```typescript
// 页面级 SEO 配置
useHead({
  title: "AI风控定价系统 - 赢睿保险科技",
  meta: [
    { name: "description", content: "..." },
    { property: "og:title", content: "..." },
  ],
});
```

### 组件开发模式 (`component-development.mdc`)

**组件结构规范:**

```
components/
├── ui/              # 基础 UI 组件
├── layout/          # 布局相关组件
├── business/        # 业务组件
└── icons/           # 图标组件
```

**样式组织:**

```vue
<style scoped>
/* 使用 @apply 指令组织复用样式 */
.product-card {
  @apply bg-white rounded-xl shadow-lg hover:shadow-xl
         transition-all duration-300 ease-out;
}
</style>
```

### 内容管理规范 (`content-management.mdc`)

**文案策略:**

1. **AI 技术先行**: 每个板块突出 AI 技术核心作用
2. **数据驱动说服**: 用具体数据展示 AI 价值
3. **场景化应用**: 通过具体场景展示实用性
4. **成果导向**: 强调业务成果和转型价值

**SEO 关键词布局:**

- 核心词：AI 保险科技、人工智能保险、智能核保
- 长尾词：保险 AI 解决方案、智能风控定价
- 行业词：保险数字化转型、保险科技、InsurTech

### 样式设计系统 (`styling-design.mdc`)

**品牌色彩:**

```css
:root {
  --color-primary-500: #10b981; /* 主品牌色 - 科技绿 */
  --color-secondary-500: #3b82f6; /* 信任蓝 */
}
```

**响应式设计:**

```vue
<style scoped>
/* 移动端优先 */
.hero-section {
  @apply py-16 px-4
         lg:py-24 lg:px-8
         xl:py-32;
}
</style>
```

### 性能优化策略 (`performance-optimization.mdc`)

**关键指标优化:**

- **LCP**: 使用 `loading="eager"` 和 `fetchpriority="high"`
- **FID**: Web Workers 处理重型计算，防抖处理用户输入
- **CLS**: 固定尺寸容器，预留动态内容空间

**代码分割:**

```typescript
// 异步组件加载
const HeavyComponent = defineAsyncComponent(
  () => import("~/components/HeavyComponent.vue")
);
```

### SEO 和可访问性 (`seo-accessibility.mdc`)

**语义化 HTML:**

```vue
<template>
  <main role="main">
    <article itemscope itemtype="https://schema.org/TechArticle">
      <header>
        <h1 itemprop="headline">页面标题</h1>
      </header>
    </article>
  </main>
</template>
```

**键盘导航:**

```vue
<script setup lang="ts">
const handleKeydown = (event: KeyboardEvent) => {
  switch (event.key) {
    case "Enter":
    case " ":
      // 处理激活
      break;
    case "Escape":
      // 处理取消
      break;
  }
};
</script>
```

## 🎯 最佳实践示例

### 产品页面开发模板

```vue
<template>
  <div itemscope itemtype="https://schema.org/SoftwareApplication">
    <!-- SEO 优化的头部 -->
    <header class="product-header">
      <h1 itemprop="name" class="product-title">
        <span class="text-primary-500">AI</span> 风控定价系统
      </h1>
      <p itemprop="description" class="product-description">
        基于 AI 大语言模型的智能风控定价解决方案
      </p>
    </header>

    <!-- 功能特性展示 -->
    <section aria-labelledby="features-title">
      <h2 id="features-title">核心功能特性</h2>
      <div class="features-grid">
        <ProductFeatureCard
          v-for="feature in features"
          :key="feature.id"
          :feature="feature"
          class="feature-card"
        />
      </div>
    </section>

    <!-- CTA 区域 -->
    <section class="cta-section">
      <UButton size="xl" class="cta-button" @click="handleCTAClick">
        预约 AI 产品演示
      </UButton>
    </section>
  </div>
</template>

<script setup lang="ts">
// SEO 配置
useHead({
  title: "AI风控定价系统 - 赢睿保险科技",
  meta: [
    { name: "description", content: "基于AI大语言模型的保险风控定价系统..." },
  ],
});

// 结构化数据
const structuredData = {
  "@context": "https://schema.org",
  "@type": "SoftwareApplication",
  name: "AI风控定价系统",
};

useHead({
  script: [
    { type: "application/ld+json", innerHTML: JSON.stringify(structuredData) },
  ],
});
</script>
```

## 🔧 开发工具配置

### VSCode 扩展推荐

```json
{
  "recommendations": [
    "Vue.volar",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-typescript-next",
    "esbenp.prettier-vscode"
  ]
}
```

### 代码格式化配置

```json
// .vscode/settings.json
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "tailwindCSS.experimental.classRegex": [
    ["class\\s*:\\s*[\"'`]([^\"'`]*)[\"'`]", "[\"'`]([^\"'`]*)[\"'`]"]
  ]
}
```

## 📊 质量检查清单

### 开发前检查

- [ ] 了解页面/组件的业务目标
- [ ] 查看相关内容文案和设计要求
- [ ] 确认技术栈和依赖版本
- [ ] 设置开发环境和工具

### 开发中检查

- [ ] 遵循组件开发规范
- [ ] 使用正确的样式系统
- [ ] 实现响应式设计
- [ ] 添加适当的类型定义
- [ ] 考虑性能优化

### 开发后检查

- [ ] 代码符合项目规范
- [ ] SEO 元数据完整
- [ ] 可访问性测试通过
- [ ] 性能指标达标
- [ ] 跨浏览器兼容性测试

## 🚨 常见问题和解决方案

### Q: 如何正确使用品牌色彩？

A: 使用 Tailwind 的 primary 色彩系统，主色为 `primary-500` (#10b981)

### Q: 组件如何处理加载状态？

A: 使用 Suspense 组件或条件渲染，提供友好的加载提示

### Q: 如何优化图片加载？

A: 使用 `@nuxt/image` 组件，配置 WebP 格式和懒加载

### Q: SEO 配置有哪些必需项？

A: title、description、Open Graph 标签、结构化数据

## 📚 参考资源

### 官方文档

- [Nuxt 3 文档](https://nuxt.com/docs)
- [Vue 3 文档](https://vuejs.org/guide/)
- [Tailwind CSS 文档](https://tailwindcss.com/docs)
- [@nuxt/ui 文档](https://ui.nuxt.com/)

### 设计系统

- [Material Design](https://material.io/design)
- [Human Interface Guidelines](https://developer.apple.com/design/human-interface-guidelines/)

### 可访问性

- [WCAG 2.1 指南](https://www.w3.org/WAI/WCAG21/quickref/)
- [MDN 可访问性](https://developer.mozilla.org/en-US/docs/Web/Accessibility)

---

**注意**: 这些规则是活的文档，会根据项目发展和最佳实践的更新而持续改进。建议定期查看和更新规则内容。
