---
description: 内容管理和文案规范，确保保险科技专业性和品牌一致性
globs: ["**/*.vue", "**/*.md", "**/赢睿科技官网栏目文案/**"]
alwaysApply: false
---

# 内容管理和文案规范

## 内容策略原则

### 1. AI 保险科技专业定位

- 始终强调 AI 技术在保险行业的创新应用
- 突出技术先进性和行业专业性
- 体现数字化转型的价值主张

### 2. 目标受众定位

- **主要受众**: 保险公司高管、IT 决策者、业务负责人
- **次要受众**: 保险中介机构、MGA 运营方、物流行业
- **语调风格**: 专业权威、创新前瞻、客户导向、数据驱动

### 3. 内容层次结构

```
首页 Hero 区域
├── 主标题：AI 赋能保险科技
├── 副标题：智慧驱动业务增长
├── 价值主张：全流程智能化解决方案
└── 行动召唤：立即体验 / 获取方案

产品体系
├── AI 智能产品线
│   ├── AI 风控定价系统
│   ├── AI 理赔管理系统
│   ├── AI 客户服务系统
│   └── AI 企业知识库系统
├── 核心业务系统
├── 营销获客平台
└── IT 基础设施

解决方案
├── 保险公司 AI 转型方案
├── 保险中介 AI 赋能方案
├── 保险 MGA AI 运营方案
└── 物流行业 AI 保险方案
```

## 文案写作规范

### 标题层级规范

```vue
<template>
  <div class="content-section">
    <!-- H1: 页面主标题 (每页只有一个) -->
    <h1 class="text-4xl lg:text-6xl font-bold text-gray-900 mb-6">
      <span class="text-primary-500">AI</span> 赋能保险科技
    </h1>

    <!-- H2: 主要区域标题 -->
    <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-8">
      为什么选择 <span class="text-primary-500">AI</span> 保险科技解决方案
    </h2>

    <!-- H3: 子区域标题 -->
    <h3 class="text-2xl lg:text-3xl font-bold text-gray-900 mb-6">
      AI 风控定价系统
    </h3>

    <!-- H4: 卡片/组件标题 -->
    <h4 class="text-xl font-bold text-gray-900 mb-4">核心技术优势</h4>

    <!-- H5: 小节标题 -->
    <h5 class="text-lg font-semibold text-gray-800 mb-3">应用场景</h5>
  </div>
</template>
```

### 产品描述文案模板

```vue
<script setup lang="ts">
// 产品信息数据结构
interface ProductContent {
  name: string;
  tagline: string; // 产品标语
  description: string; // 产品描述
  coreCapabilities: string; // 核心能力
  applicationValue: string; // 应用价值
  usageScenarios: string[]; // 适用场景
  keyFeatures: string[]; // 关键特性
  metrics: {
    // 关键指标
    efficiency: string;
    accuracy: string;
    cost_reduction: string;
  };
}

// AI 风控定价系统内容示例
const aiRiskPricingContent: ProductContent = {
  name: "AI 风控定价系统",
  tagline: "分钟级部署，高精度定价，长尾市场拓展",
  description:
    "基于 AI 大语言模型和行业大数据构建的智能风控定价引擎，运用深度学习算法实现货运险精准风险评估和动态定价，助力保险机构快速拓展长尾市场。",
  coreCapabilities: "AI 大语言模型 + 行业大数据 + 深度学习算法",
  applicationValue: "分钟级系统部署，风险识别准确率达 95%，定价精度提升 3 倍",
  usageScenarios: [
    "货运险精准风控与动态定价",
    "新兴市场风险评估与产品创新",
    "长尾客户自动化承保决策",
    "实时风险监控与预警管理",
  ],
  keyFeatures: [
    "智能风险模型：基于机器学习的多维度风险评估",
    "动态定价引擎：实时调整价格策略优化盈利能力",
    "合规规则引擎：内置监管要求确保合规性",
    "API 开放接口：快速集成现有业务系统",
  ],
  metrics: {
    efficiency: "承保效率提升 10 倍",
    accuracy: "风险识别准确率 95%",
    cost_reduction: "运营成本降低 60%",
  },
};
</script>
```

### 解决方案文案模板

```vue
<script setup lang="ts">
// 解决方案内容结构
interface SolutionContent {
  title: string;
  subtitle: string;
  overview: string;
  targetAudience: string;
  painPoints: string[]; // 传统痛点
  aiSolution: string; // AI 解决方案
  coreValue: string[]; // 核心价值
  applicationScenarios: string[]; // 应用场景
  successMetrics: {
    // 成功指标
    [key: string]: string;
  };
  customerCase: {
    // 客户案例
    challenge: string;
    solution: string;
    result: string;
  };
}

// 保险公司 AI 转型方案示例
const insuranceCompanyAI: SolutionContent = {
  title: "保险公司 AI 转型方案",
  subtitle: "从传统到智能的全面升级",
  overview:
    "针对保险公司数字化转型需求，提供以 AI 技术为核心的全流程智能化解决方案，涵盖智能核保、AI 理赔、智能客服等关键业务环节，助力保险公司实现运营效率和客户体验的双重提升。",
  targetAudience: "中大型保险公司、区域性保险机构、专业保险公司",
  painPoints: [
    "人工核保耗时长，效率低下影响客户体验",
    "理赔流程复杂，周期长导致客户满意度下降",
    "客服人力成本高，服务质量难以标准化",
    "风险识别依赖经验，缺乏数据驱动决策",
  ],
  aiSolution:
    "构建以 AI 大语言模型为核心的智能保险业务平台，实现秒级核保决策、智能理赔处理、7×24 小时智能客服和精准风险控制。",
  coreValue: [
    "智能核保引擎：秒级风险评估，核保效率提升 10 倍",
    "AI 理赔系统：自动化处理 70% 理赔案件，周期缩短至 1 天",
    "智能客服平台：30 秒响应，减少 90% 人工干预",
    "风控决策支持：AI 模型识别潜在风险，准确率达 95%",
  ],
  applicationScenarios: [
    "车险快速核保与智能定价",
    "健康险智能核保与风险评估",
    "财产险自动化理赔与定损",
    "多渠道智能客户服务",
  ],
  successMetrics: {
    "IT 成本降低": "50%",
    人工操作减少: "80%",
    客户满意度: "98%",
    理赔处理效率: "提升 5 倍",
  },
  customerCase: {
    challenge: "某中型财险公司面临核保效率低、理赔周期长的问题，影响市场竞争力",
    solution: "部署 AI 核保引擎和智能理赔系统，实现业务流程自动化改造",
    result:
      "核保时间从 4 小时缩短至分钟级，理赔周期缩短 60%，客户满意度提升至 95%",
  },
};
</script>
```

## SEO 内容优化规范

### 关键词策略

```vue
<script setup lang="ts">
// SEO 关键词配置
const seoKeywords = {
  // 核心关键词
  primary: [
    "AI保险科技",
    "人工智能保险",
    "智能核保",
    "AI理赔",
    "保险AI解决方案",
  ],

  // 长尾关键词
  longTail: [
    "保险公司AI数字化转型",
    "智能风控定价系统",
    "AI客户服务系统",
    "保险中介AI赋能方案",
    "货运保险AI风控",
  ],

  // 行业关键词
  industry: [
    "保险科技",
    "InsurTech",
    "保险数字化转型",
    "智能保险平台",
    "保险业务自动化",
  ],

  // 地域关键词
  location: ["上海保险科技公司", "AI保险解决方案提供商", "专业保险技术服务"],
};

// 页面 SEO 配置函数
const configureSEO = (pageType: string, title: string, description: string) => {
  const keywords = [
    ...seoKeywords.primary.slice(0, 3),
    ...seoKeywords.longTail.slice(0, 2),
    ...seoKeywords.industry.slice(0, 2),
  ].join(", ");

  useHead({
    title: `${title} - 赢睿保险科技`,
    meta: [
      { name: "description", content: description },
      { name: "keywords", content: keywords },
      { property: "og:title", content: `${title} - 赢睿保险科技` },
      { property: "og:description", content: description },
      { property: "og:type", content: "website" },
      { name: "twitter:title", content: `${title} - 赢睿保险科技` },
      { name: "twitter:description", content: description },
    ],
  });
};
</script>
```

### 结构化数据标记

```vue
<script setup lang="ts">
// JSON-LD 结构化数据
const structuredData = {
  "@context": "https://schema.org",
  "@type": "TechArticle",
  headline: "AI 风控定价系统 - 智能保险科技解决方案",
  description: "基于AI大语言模型的保险风控定价系统，实现分钟级部署和高精度定价",
  author: {
    "@type": "Organization",
    name: "赢睿保险科技",
    url: "https://www.yingrtech.com",
  },
  publisher: {
    "@type": "Organization",
    name: "赢睿保险科技",
    logo: {
      "@type": "ImageObject",
      url: "https://www.yingrtech.com/logo.png",
    },
  },
  datePublished: "2024-01-01",
  dateModified: "2024-01-01",
  mainEntityOfPage: {
    "@type": "WebPage",
    "@id": "https://www.yingrtech.com/products/ai-risk-pricing",
  },
};

// 产品结构化数据
const productStructuredData = {
  "@context": "https://schema.org",
  "@type": "SoftwareApplication",
  name: "AI 风控定价系统",
  description: "基于人工智能的保险风控定价解决方案",
  applicationCategory: "BusinessApplication",
  operatingSystem: "Web-based",
  offers: {
    "@type": "Offer",
    priceCurrency: "CNY",
    price: "0",
    description: "联系获取报价",
  },
  provider: {
    "@type": "Organization",
    name: "赢睿保险科技",
  },
};

// 在页面中注入结构化数据
useHead({
  script: [
    {
      type: "application/ld+json",
      innerHTML: JSON.stringify(structuredData),
    },
  ],
});
</script>
```

## 多语言内容管理

### 内容国际化准备

```vue
<script setup lang="ts">
// 内容配置接口
interface ContentConfig {
  locale: string;
  messages: {
    [key: string]: string | ContentConfig["messages"];
  };
}

// 中文内容配置
const zhCN: ContentConfig = {
  locale: "zh-CN",
  messages: {
    common: {
      brand_name: "赢睿保险科技",
      tagline: "AI 赋能保险科技，智慧驱动业务增长",
      learn_more: "了解详情",
      contact_us: "联系我们",
      request_demo: "预约演示",
    },
    navigation: {
      products: "产品体系",
      solutions: "解决方案",
      services: "服务体系",
      cases: "案例中心",
      about: "关于我们",
    },
    products: {
      ai_risk_pricing: {
        name: "AI 风控定价系统",
        description: "基于 AI 大语言模型和行业大数据构建的智能风控定价引擎",
        core_capabilities: "AI 大语言模型 + 行业大数据",
        application_value: "分钟级部署，高精度定价，长尾市场拓展",
      },
    },
  },
};

// 英文内容配置（预留）
const enUS: ContentConfig = {
  locale: "en-US",
  messages: {
    common: {
      brand_name: "Winray InsurTech",
      tagline: "AI-Powered Insurance Technology, Driving Smart Business Growth",
      learn_more: "Learn More",
      contact_us: "Contact Us",
      request_demo: "Request Demo",
    },
    // ... 其他英文内容
  },
};
</script>
```

## 内容质量控制

### 文案审核清单

```typescript
// 内容质量检查清单
interface ContentQualityChecklist {
  // 品牌一致性
  brandConsistency: {
    usesCorrectBrandName: boolean; // 使用正确的品牌名称
    maintainsBrandTone: boolean; // 保持品牌语调
    followsVisualGuidelines: boolean; // 遵循视觉规范
  };

  // 技术准确性
  technicalAccuracy: {
    aiTermsCorrect: boolean; // AI 术语使用准确
    industryTermsCorrect: boolean; // 行业术语使用准确
    dataAndMetricsVerified: boolean; // 数据和指标已验证
  };

  // 用户体验
  userExperience: {
    clearCallToAction: boolean; // 清晰的行动召唤
    logicalInformationFlow: boolean; // 逻辑清晰的信息流
    mobileOptimized: boolean; // 移动端优化
  };

  // SEO 优化
  seoOptimization: {
    titleOptimized: boolean; // 标题优化
    metaDescriptionOptimized: boolean; // 描述优化
    keywordsNaturallyIntegrated: boolean; // 关键词自然融入
    structuredDataIncluded: boolean; // 包含结构化数据
  };
}

// 内容发布前检查函数
const validateContent = (content: any): ContentQualityChecklist => {
  return {
    brandConsistency: {
      usesCorrectBrandName:
        content.includes("赢睿保险科技") || content.includes("赢睿科技"),
      maintainsBrandTone: checkBrandTone(content),
      followsVisualGuidelines: checkVisualGuidelines(content),
    },
    technicalAccuracy: {
      aiTermsCorrect: validateAITerms(content),
      industryTermsCorrect: validateIndustryTerms(content),
      dataAndMetricsVerified: validateMetrics(content),
    },
    userExperience: {
      clearCallToAction: hasClearCTA(content),
      logicalInformationFlow: checkInformationFlow(content),
      mobileOptimized: checkMobileOptimization(content),
    },
    seoOptimization: {
      titleOptimized: checkTitleOptimization(content),
      metaDescriptionOptimized: checkMetaDescription(content),
      keywordsNaturallyIntegrated: checkKeywordIntegration(content),
      structuredDataIncluded: hasStructuredData(content),
    },
  };
};
```

### 内容更新工作流

```vue
<script setup lang="ts">
// 内容更新状态管理
interface ContentUpdateWorkflow {
  status: "draft" | "review" | "approved" | "published";
  author: string;
  reviewer?: string;
  lastModified: Date;
  publishDate?: Date;
  version: string;
}

// 内容版本控制
const manageContentVersion = (
  content: any,
  workflow: ContentUpdateWorkflow
) => {
  const contentHistory = {
    current: content,
    workflow: workflow,
    changeLog: [
      {
        version: workflow.version,
        date: workflow.lastModified,
        author: workflow.author,
        changes: "更新产品功能描述和关键指标",
      },
    ],
  };

  return contentHistory;
};

// 内容发布管理
const publishContent = async (
  contentId: string,
  workflow: ContentUpdateWorkflow
) => {
  if (workflow.status !== "approved") {
    throw new Error("内容必须经过审核才能发布");
  }

  try {
    // 发布内容到生产环境
    await deployContent(contentId);

    // 更新工作流状态
    workflow.status = "published";
    workflow.publishDate = new Date();

    // 通知相关人员
    await notifyStakeholders(contentId, workflow);
  } catch (error) {
    console.error("内容发布失败:", error);
    throw error;
  }
};
</script>
```

## 内容分析和优化

### 内容效果跟踪

```vue
<script setup lang="ts">
// 内容效果分析接口
interface ContentAnalytics {
  pageViews: number;
  uniqueVisitors: number;
  averageTimeOnPage: number;
  bounceRate: number;
  conversionRate: number;
  topExitPages: string[];
  searchKeywords: string[];
  userFeedback: {
    rating: number;
    comments: string[];
  };
}

// 内容优化建议生成
const generateOptimizationSuggestions = (analytics: ContentAnalytics) => {
  const suggestions = [];

  if (analytics.bounceRate > 0.7) {
    suggestions.push({
      type: "content",
      priority: "high",
      suggestion: "跳出率较高，建议优化页面内容吸引力和相关性",
    });
  }

  if (analytics.averageTimeOnPage < 60) {
    suggestions.push({
      type: "engagement",
      priority: "medium",
      suggestion: "页面停留时间较短，考虑增加互动元素或视频内容",
    });
  }

  if (analytics.conversionRate < 0.05) {
    suggestions.push({
      type: "cta",
      priority: "high",
      suggestion: "转化率偏低，建议优化行动召唤按钮的位置和文案",
    });
  }

  return suggestions;
};
</script>
```
