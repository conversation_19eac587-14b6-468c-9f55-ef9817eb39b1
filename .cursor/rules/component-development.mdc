---
description: Vue 组件开发规范和最佳实践，专注于保险科技官网组件设计
globs: ["**/components/**/*.vue"]
alwaysApply: false
---

# 组件开发规范

## 组件命名和结构

### 文件命名规范

```
components/
├── ui/              # 基础 UI 组件
│   ├── Button.vue
│   ├── Card.vue
│   └── Modal.vue
├── layout/          # 布局相关组件
│   ├── Header.vue
│   ├── Footer.vue
│   └── Navigation.vue
├── business/        # 业务组件
│   ├── ProductCard.vue
│   ├── SolutionSection.vue
│   └── TestimonialCard.vue
└── icons/           # 图标组件
    ├── AiIcon.vue
    └── InsuranceIcon.vue
```

### 组件命名约定

- 组件文件名使用 PascalCase: `ProductCard.vue`
- 多词组件名必须使用连字符: `<product-card>` 或 `<ProductCard>`
- 基础组件使用特定前缀: `BaseButton.vue`, `BaseCard.vue`
- 业务特定组件使用描述性名称: `AiSolutionCard.vue`

## 组件模板规范

### 标准组件结构

```vue
<template>
  <!-- 使用语义化的 HTML 结构 -->
  <article
    class="product-card bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300"
  >
    <!-- 头部区域 -->
    <header class="p-6 border-b border-gray-100">
      <div class="flex items-center space-x-4">
        <div
          class="w-12 h-12 bg-primary-500 rounded-lg flex items-center justify-center"
        >
          <UIcon :name="icon" class="w-6 h-6 text-white" />
        </div>
        <div>
          <h3 class="text-xl font-bold text-gray-900">{{ title }}</h3>
          <p class="text-sm text-primary-600">{{ category }}</p>
        </div>
      </div>
    </header>

    <!-- 内容区域 -->
    <main class="p-6">
      <p class="text-gray-600 mb-4 leading-relaxed">{{ description }}</p>

      <!-- 功能特性列表 -->
      <ul class="space-y-2 mb-6">
        <li
          v-for="feature in features"
          :key="feature"
          class="flex items-start space-x-2"
        >
          <UIcon
            name="i-heroicons-check-circle"
            class="w-5 h-5 text-primary-500 mt-0.5 flex-shrink-0"
          />
          <span class="text-sm text-gray-700">{{ feature }}</span>
        </li>
      </ul>
    </main>

    <!-- 底部操作区域 -->
    <footer class="px-6 pb-6">
      <UButton
        :to="detailLink"
        class="w-full bg-primary-500 hover:bg-primary-600 text-white"
        @click="handleLearnMore"
      >
        了解详情 →
      </UButton>
    </footer>
  </article>
</template>
```

### 条件渲染最佳实践

```vue
<template>
  <div class="solution-section">
    <!-- 使用 v-if 进行条件渲染 -->
    <div v-if="isLoading" class="flex justify-center items-center h-64">
      <UIcon
        name="i-heroicons-arrow-path"
        class="w-8 h-8 animate-spin text-primary-500"
      />
      <span class="ml-2 text-gray-600">加载中...</span>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="hasError" class="text-center py-16">
      <UIcon
        name="i-heroicons-exclamation-triangle"
        class="w-16 h-16 text-red-500 mx-auto mb-4"
      />
      <p class="text-gray-600">加载失败，请刷新页面重试</p>
    </div>

    <!-- 正常内容 -->
    <div v-else class="grid lg:grid-cols-3 gap-8">
      <SolutionCard
        v-for="solution in solutions"
        :key="solution.id"
        :solution="solution"
        @select="handleSolutionSelect"
      />
    </div>

    <!-- 空状态 -->
    <div
      v-if="!isLoading && !hasError && solutions.length === 0"
      class="text-center py-16"
    >
      <UIcon
        name="i-heroicons-document-magnifying-glass"
        class="w-16 h-16 text-gray-400 mx-auto mb-4"
      />
      <p class="text-gray-500">暂无解决方案数据</p>
    </div>
  </div>
</template>
```

## 组件 Props 设计

### Props 接口定义

```vue
<script setup lang="ts">
// 产品卡片组件的 Props 接口
interface ProductCardProps {
  // 必需属性
  id: string;
  title: string;
  description: string;

  // 可选属性
  category?: string;
  icon?: string;
  features?: string[];
  detailLink?: string;

  // 布尔属性
  isHighlighted?: boolean;
  showFeatures?: boolean;

  // 回调函数
  onSelect?: (id: string) => void;
}

// 使用 withDefaults 设置默认值
const props = withDefaults(defineProps<ProductCardProps>(), {
  category: "AI智能产品",
  icon: "i-heroicons-cpu-chip",
  features: () => [],
  detailLink: "#",
  isHighlighted: false,
  showFeatures: true,
});

// Emits 定义
const emit = defineEmits<{
  select: [id: string];
  "learn-more": [productId: string];
}>();
</script>
```

### Props 验证规范

```vue
<script setup lang="ts">
// 复杂的 Props 验证
interface SolutionData {
  id: string;
  name: string;
  type: "company" | "intermediary" | "mga" | "logistics";
  features: string[];
  metrics: {
    efficiency: number;
    cost_reduction: number;
    accuracy: number;
  };
}

interface SolutionSectionProps {
  solutions: SolutionData[];
  sectionTitle: string;
  maxDisplayCount?: number;
  layout?: "grid" | "list" | "carousel";
}

const props = withDefaults(defineProps<SolutionSectionProps>(), {
  maxDisplayCount: 6,
  layout: "grid",
});

// 计算属性进行数据处理
const displayedSolutions = computed(() => {
  return props.solutions.slice(0, props.maxDisplayCount);
});
</script>
```

## 事件处理规范

### 事件命名约定

```vue
<script setup lang="ts">
// 事件处理函数命名：handle + 动作 + 对象
const handleProductSelect = (productId: string) => {
  emit("select", productId);

  // 可选：添加分析追踪
  // analytics.track('product_selected', { product_id: productId })
};

const handleLearnMoreClick = (productId: string) => {
  emit("learn-more", productId);

  // 导航到详情页
  navigateTo(`/products/${productId}`);
};

const handleMenuToggle = (menuId: string) => {
  activeMenu.value = activeMenu.value === menuId ? null : menuId;
};

const handleFormSubmit = async (formData: ContactForm) => {
  try {
    isSubmitting.value = true;
    await submitContactForm(formData);
    showSuccessMessage.value = true;
  } catch (error) {
    console.error("表单提交失败:", error);
    showErrorMessage.value = true;
  } finally {
    isSubmitting.value = false;
  }
};
</script>
```

### 自定义事件设计

```vue
<script setup lang="ts">
// 复杂事件数据结构
interface ProductSelectEvent {
  productId: string;
  category: string;
  source: "card" | "list" | "search";
}

interface FilterChangeEvent {
  filters: {
    category?: string;
    priceRange?: [number, number];
    features?: string[];
  };
  resultCount: number;
}

const emit = defineEmits<{
  "product:select": [event: ProductSelectEvent];
  "filter:change": [event: FilterChangeEvent];
  "section:scroll": [sectionId: string];
}>();

// 发送结构化事件
const emitProductSelect = (
  productId: string,
  source: "card" | "list" | "search"
) => {
  emit("product:select", {
    productId,
    category: props.category,
    source,
  });
};
</script>
```

## 样式和设计规范

### CSS 类命名约定

```vue
<template>
  <!-- 使用 BEM 命名法的概念，结合 Tailwind CSS -->
  <div class="ai-solution-card">
    <!-- 卡片头部 -->
    <div
      class="ai-solution-card__header bg-gradient-to-r from-primary-500 to-primary-600 p-6 rounded-t-xl"
    >
      <div class="ai-solution-card__title-group flex items-center space-x-4">
        <div
          class="ai-solution-card__icon w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center"
        >
          <UIcon :name="solution.icon" class="w-6 h-6 text-white" />
        </div>
        <div>
          <h3 class="ai-solution-card__title text-xl font-bold text-white">
            {{ solution.name }}
          </h3>
          <p class="ai-solution-card__category text-primary-100 text-sm">
            {{ solution.category }}
          </p>
        </div>
      </div>
    </div>

    <!-- 卡片内容 -->
    <div class="ai-solution-card__content p-6 bg-white">
      <p class="ai-solution-card__description text-gray-600 mb-4">
        {{ solution.description }}
      </p>

      <!-- 核心指标 -->
      <div class="ai-solution-card__metrics grid grid-cols-3 gap-4 mb-6">
        <div class="ai-solution-card__metric text-center">
          <div class="text-2xl font-bold text-primary-600">
            {{ solution.metrics.efficiency }}%
          </div>
          <div class="text-xs text-gray-500">效率提升</div>
        </div>
        <div class="ai-solution-card__metric text-center">
          <div class="text-2xl font-bold text-primary-600">
            {{ solution.metrics.cost_reduction }}%
          </div>
          <div class="text-xs text-gray-500">成本降低</div>
        </div>
        <div class="ai-solution-card__metric text-center">
          <div class="text-2xl font-bold text-primary-600">
            {{ solution.metrics.accuracy }}%
          </div>
          <div class="text-xs text-gray-500">准确率</div>
        </div>
      </div>
    </div>

    <!-- 卡片底部 -->
    <div class="ai-solution-card__footer p-6 pt-0">
      <UButton
        class="ai-solution-card__cta w-full bg-primary-500 hover:bg-primary-600 text-white"
        @click="handleLearnMore(solution.id)"
      >
        了解 {{ solution.name }} →
      </UButton>
    </div>
  </div>
</template>

<style scoped>
/* 组件特定的样式 */
.ai-solution-card {
  @apply bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden;
}

.ai-solution-card:hover {
  @apply transform -translate-y-1;
}

.ai-solution-card__metric {
  @apply transition-colors duration-200;
}

.ai-solution-card:hover .ai-solution-card__metric {
  @apply text-primary-700;
}
</style>
```

### 响应式设计模式

```vue
<template>
  <!-- 移动端优先的响应式设计 -->
  <section class="solutions-showcase py-16 lg:py-24">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
      <!-- 标题区域 -->
      <div class="text-center mb-12 lg:mb-16">
        <h2
          class="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 lg:mb-6"
        >
          <span class="text-primary-500">AI</span> 驱动的解决方案
        </h2>
        <p class="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto">
          针对不同行业特点，提供专业化的 AI 保险科技解决方案
        </p>
      </div>

      <!-- 解决方案网格 -->
      <div
        class="grid gap-6 sm:gap-8 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
      >
        <SolutionCard
          v-for="solution in solutions"
          :key="solution.id"
          :solution="solution"
          class="solution-card"
          @select="handleSolutionSelect"
        />
      </div>
    </div>
  </section>
</template>

<style scoped>
/* 响应式动画效果 */
@media (min-width: 1024px) {
  .solution-card {
    @apply hover:scale-105;
  }
}

/* 移动端优化 */
@media (max-width: 768px) {
  .solutions-showcase {
    @apply py-12;
  }

  .solution-card {
    @apply hover:shadow-lg;
  }
}
</style>
```

## 可复用组件模式

### 高阶组件（HOC）模式

```vue
<!-- BaseCard.vue - 基础卡片组件 -->
<template>
  <div
    class="base-card bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
    :class="cardClasses"
  >
    <!-- 头部插槽 -->
    <header v-if="$slots.header" class="base-card__header">
      <slot name="header" />
    </header>

    <!-- 默认内容插槽 -->
    <main class="base-card__content p-6">
      <slot />
    </main>

    <!-- 底部插槽 -->
    <footer v-if="$slots.footer" class="base-card__footer">
      <slot name="footer" />
    </footer>
  </div>
</template>

<script setup lang="ts">
interface BaseCardProps {
  variant?: "default" | "highlighted" | "minimal";
  size?: "sm" | "md" | "lg";
  hoverable?: boolean;
}

const props = withDefaults(defineProps<BaseCardProps>(), {
  variant: "default",
  size: "md",
  hoverable: true,
});

const cardClasses = computed(() => ({
  "base-card--highlighted": props.variant === "highlighted",
  "base-card--minimal": props.variant === "minimal",
  "base-card--sm": props.size === "sm",
  "base-card--lg": props.size === "lg",
  "base-card--hoverable": props.hoverable,
}));
</script>
```

### 组合式组件模式

```vue
<!-- ProductShowcase.vue - 产品展示组合组件 -->
<template>
  <section class="product-showcase">
    <!-- 使用基础卡片组件 -->
    <BaseCard
      v-for="product in products"
      :key="product.id"
      variant="highlighted"
      hoverable
      class="product-showcase__card"
      @click="handleProductClick(product.id)"
    >
      <!-- 头部：产品图标和标题 -->
      <template #header>
        <div
          class="flex items-center space-x-4 p-6 bg-gradient-to-r from-primary-500 to-primary-600 text-white rounded-t-xl"
        >
          <UIcon :name="product.icon" class="w-8 h-8" />
          <div>
            <h3 class="text-xl font-bold">{{ product.name }}</h3>
            <p class="text-primary-100 text-sm">{{ product.category }}</p>
          </div>
        </div>
      </template>

      <!-- 主要内容：产品描述和特性 -->
      <div class="space-y-4">
        <p class="text-gray-600">{{ product.description }}</p>

        <ProductFeatures :features="product.features" />

        <ProductMetrics :metrics="product.metrics" />
      </div>

      <!-- 底部：行动按钮 -->
      <template #footer>
        <div class="p-6 pt-0 space-y-3">
          <UButton
            class="w-full bg-primary-500 hover:bg-primary-600 text-white"
            @click.stop="handleLearnMore(product.id)"
          >
            了解 {{ product.name }}
          </UButton>
          <UButton
            variant="outline"
            class="w-full border-primary-500 text-primary-600 hover:bg-primary-50"
            @click.stop="handleRequestDemo(product.id)"
          >
            预约演示
          </UButton>
        </div>
      </template>
    </BaseCard>
  </section>
</template>
```

## 性能优化组件模式

### 虚拟滚动组件

```vue
<!-- VirtualList.vue - 虚拟滚动列表组件 -->
<template>
  <div
    class="virtual-list overflow-auto"
    :style="{ height: containerHeight + 'px' }"
    @scroll="handleScroll"
  >
    <div :style="{ height: totalHeight + 'px', position: 'relative' }">
      <div
        v-for="item in visibleItems"
        :key="item.id"
        class="virtual-list__item"
        :style="{
          position: 'absolute',
          top: item.top + 'px',
          height: itemHeight + 'px',
          width: '100%',
        }"
      >
        <slot :item="item.data" :index="item.index" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface VirtualListProps {
  items: any[];
  itemHeight: number;
  containerHeight: number;
  overscan?: number;
}

const props = withDefaults(defineProps<VirtualListProps>(), {
  overscan: 5,
});

const scrollTop = ref(0);

const totalHeight = computed(() => props.items.length * props.itemHeight);

const visibleItems = computed(() => {
  const start = Math.floor(scrollTop.value / props.itemHeight);
  const end = Math.min(
    start +
      Math.ceil(props.containerHeight / props.itemHeight) +
      props.overscan,
    props.items.length
  );

  return props.items.slice(start, end).map((item, index) => ({
    id: item.id || start + index,
    data: item,
    index: start + index,
    top: (start + index) * props.itemHeight,
  }));
});

const handleScroll = (event: Event) => {
  scrollTop.value = (event.target as HTMLElement).scrollTop;
};
</script>
```

### 懒加载图片组件

```vue
<!-- LazyImage.vue - 懒加载图片组件 -->
<template>
  <div
    ref="imageContainer"
    class="lazy-image"
    :class="{ 'lazy-image--loaded': isLoaded }"
  >
    <div
      v-if="!isLoaded"
      class="lazy-image__placeholder bg-gray-200 animate-pulse"
      :style="{ aspectRatio: `${width} / ${height}` }"
    >
      <div class="flex items-center justify-center h-full">
        <UIcon name="i-heroicons-photo" class="w-12 h-12 text-gray-400" />
      </div>
    </div>

    <NuxtImg
      v-show="isLoaded"
      :src="src"
      :alt="alt"
      :width="width"
      :height="height"
      :loading="eager ? 'eager' : 'lazy'"
      class="lazy-image__img w-full h-full object-cover"
      @load="handleImageLoad"
      @error="handleImageError"
    />

    <div
      v-if="hasError"
      class="lazy-image__error bg-red-50 text-red-600 p-4 text-center"
    >
      <UIcon
        name="i-heroicons-exclamation-triangle"
        class="w-8 h-8 mx-auto mb-2"
      />
      <p class="text-sm">图片加载失败</p>
    </div>
  </div>
</template>

<script setup lang="ts">
interface LazyImageProps {
  src: string;
  alt: string;
  width: number;
  height: number;
  eager?: boolean;
}

const props = defineProps<LazyImageProps>();

const imageContainer = ref<HTMLElement>();
const isLoaded = ref(false);
const hasError = ref(false);

const handleImageLoad = () => {
  isLoaded.value = true;
  hasError.value = false;
};

const handleImageError = () => {
  hasError.value = true;
  isLoaded.value = false;
};

// 使用 Intersection Observer 进行懒加载
onMounted(() => {
  if (!props.eager && imageContainer.value) {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            // 图片进入视口，开始加载
            observer.unobserve(entry.target);
          }
        });
      },
      { threshold: 0.1 }
    );

    observer.observe(imageContainer.value);

    onUnmounted(() => {
      observer.disconnect();
    });
  }
});
</script>
```
