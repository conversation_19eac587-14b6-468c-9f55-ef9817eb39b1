# 赢睿科技官网 Cursor Rules

本项目已配置完整的 Cursor Rules 规则系统，旨在确保开发团队遵循最佳实践，保持代码质量和品牌一致性。

## 📁 规则文件结构

```
.cursor/rules/
├── README.mdc                    # 📖 使用指南和规则总览
├── project-overview.mdc          # 🎯 项目总体开发规范
├── nuxt-vue-development.mdc      # ⚡ Nuxt 3 + Vue 3 开发规范
├── component-development.mdc     # 🧩 组件开发规范和模式
├── content-management.mdc        # 📝 内容管理和文案规范
├── styling-design.mdc            # 🎨 样式和设计系统规范
├── performance-optimization.mdc  # 🚀 性能优化最佳实践
└── seo-accessibility.mdc         # 🔍 SEO 和可访问性规范
```

## 🚀 快速开始

1. **查看总览**: 首先阅读 `README.mdc` 了解整体规则架构
2. **选择相关规则**: 根据开发任务选择对应的规则文件
3. **遵循规范**: 在开发过程中参考相关规范和最佳实践
4. **质量检查**: 使用规则中的检查清单确保代码质量

## 🎯 核心规范要点

### 技术栈

- **框架**: Nuxt 3 + Vue 3 + TypeScript
- **UI**: @nuxt/ui + Tailwind CSS 4.x
- **优化**: @nuxt/image + @vueuse/motion

### 品牌定位

- **行业**: AI 保险科技
- **主色**: #10b981 (科技绿)
- **语调**: 专业权威、创新前瞻、客户导向、数据驱动

### 开发原则

- 中文优先，保持专业形象
- 响应式设计，移动端优先
- 性能优化，关注用户体验
- SEO 友好，确保可访问性

## 📚 使用方式

这些规则文件会自动被 Cursor 识别和应用：

- **Always Apply**: `project-overview.mdc` 和 `README.mdc` 始终生效
- **Auto Attached**: 其他规则根据文件类型和路径自动附加
- **Manual**: 可以通过 `@ruleName` 手动引用特定规则

## 🔧 开发工具支持

规则文件包含了完整的开发工具配置建议：

- VSCode 扩展推荐
- 代码格式化配置
- 性能监控工具
- SEO 检查工具

---

**注意**: 规则文件使用 MDC 格式，包含元数据配置和详细的规范说明。建议开发团队定期查看和更新这些规则。
