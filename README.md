# 赢睿保险科技官网 🚀

> 基于 Nuxt 4 + Tailwind 4 的现代化 AI 保险科技企业官网

[![Nuxt 4](https://img.shields.io/badge/Nuxt-4.0.3-00DC82?logo=nuxt.js&logoColor=white)](https://nuxt.com/)
[![Tailwind CSS 4](https://img.shields.io/badge/Tailwind%20CSS-4.1.11-06B6D4?logo=tailwind-css&logoColor=white)](https://tailwindcss.com/)
[![Vue 3](https://img.shields.io/badge/Vue-3.5.18-4FC08D?logo=vue.js&logoColor=white)](https://vuejs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.9.2-3178C6?logo=typescript&logoColor=white)](https://www.typescriptlang.org/)

## 📋 项目概述

赢睿保险科技官网是一个专注于展示 AI 保险科技解决方案的现代化企业网站。项目采用最新的 **Nuxt 4** 和 **Tailwind CSS 4** 技术栈，提供卓越的用户体验和开发体验。

### 🎯 核心特色

- **AI 保险科技主题** - 专业展示人工智能在保险行业的应用
- **现代化技术栈** - 使用最新版本的 Nuxt 4 和 Tailwind 4
- **响应式设计** - 移动端优先，适配所有设备
- **性能优化** - 内置 SEO 优化和 Core Web Vitals 优化
- **类型安全** - 完整的 TypeScript 支持

## 🛠️ 技术栈

### 核心框架

- **[Nuxt 4.0.3](https://nuxt.com/)** - Vue.js 全栈框架
- **[Vue 3.5.18](https://vuejs.org/)** - 渐进式 JavaScript 框架
- **[TypeScript 5.9.2](https://www.typescriptlang.org/)** - JavaScript 的超集

### 样式和 UI

- **[Tailwind CSS 4.1.11](https://tailwindcss.com/)** - 原子化 CSS 框架
- **[@nuxt/ui 3.3.0](https://ui.nuxt.com/)** - 基于 Headless UI 的组件库
- **[@tailwindcss/typography](https://tailwindcss.com/docs/typography-plugin)** - 排版插件

### 功能模块

- **[@nuxt/image 1.11.0](https://image.nuxt.com/)** - 图像优化
- **[@nuxt/scripts 0.11.10](https://scripts.nuxt.com/)** - 脚本管理
- **[@vueuse/motion 3.0.3](https://motion.vueuse.org/)** - 动画库
- **[better-sqlite3 12.2.0](https://github.com/WiseLibs/better-sqlite3)** - SQLite 数据库
- **[Zod 4.0.17](https://zod.dev/)** - 类型验证库

## 🚨 重要版本变更说明

### Nuxt 4 主要变更

#### 1. 目录结构变化

```bash
# Nuxt 3 结构
project/
├── components/
├── pages/
├── layouts/
└── nuxt.config.ts

# Nuxt 4 结构 (本项目)
project/
├── app/                 # 新的应用目录
│   ├── components/      # 组件目录
│   ├── pages/          # 页面目录
│   ├── layouts/        # 布局目录
│   ├── assets/         # 资源目录
│   └── app.vue         # 根组件
├── nuxt.config.ts
└── package.json
```

#### 2. 配置文件更新

```typescript
// nuxt.config.ts - Nuxt 4 配置
export default defineNuxtConfig({
  compatibilityDate: "2025-07-15", // 新增兼容性日期
  devtools: { enabled: true },
  modules: [
    "@nuxt/image",
    "@nuxt/scripts", // 新的脚本管理模块
    "@nuxt/ui",
    "@tailwindcss/vite", // Tailwind 4 Vite 插件
  ],

  // 新的 Nitro 预渲染配置
  nitro: {
    prerender: {
      failOnError: false,
      routes: ["/"],
    },
  },
});
```

#### 3. TypeScript 配置更新

```json
// tsconfig.json - 引用 Nuxt 4 生成的配置
{
  "references": [
    { "path": "./.nuxt/tsconfig.app.json" },
    { "path": "./.nuxt/tsconfig.server.json" },
    { "path": "./.nuxt/tsconfig.shared.json" },
    { "path": "./.nuxt/tsconfig.node.json" }
  ]
}
```

### Tailwind CSS 4 主要变更

#### 1. 导入方式变化

```css
/* main.css - Tailwind 4 新语法 */
@import "tailwindcss"; /* 新的导入方式 */
@import "@nuxt/ui";

/* 旧版本 Tailwind 3 */
@tailwind base;
@tailwind components;
@tailwind utilities;
```

#### 2. 主题配置新语法

```css
/* Tailwind 4 @theme 指令 */
@theme {
  --color-primary-50: #f5f5f2;
  --color-primary-500: #008891;
  --color-primary-600: #007a82;
  --color-winray-500: #008891; /* 自定义品牌色 */
}

/* 替代了 tailwind.config.js 中的 theme 配置 */
```

#### 3. Vite 插件集成

```typescript
// nuxt.config.ts
export default defineNuxtConfig({
  modules: [
    "@tailwindcss/vite", // 使用 Vite 插件而非 @nuxtjs/tailwindcss
  ],
});
```

#### 4. 层级系统增强

```css
@layer base {
  /* 基础样式 - 全局字体设置 */
  * {
    font-family: "Roboto", "PingFang SC", sans-serif !important;
  }
}

@layer components {
  /* 组件样式 - 按钮、卡片等 */
  .btn {
    /* 自定义组件样式 */
  }
}
```

## 🚀 快速开始

### 环境要求

- **Node.js**: >= 18.0.0
- **npm**: >= 9.0.0 (推荐使用 pnpm 或 yarn)

### 安装依赖

```bash
# 使用 npm
npm install

# 使用 pnpm (推荐)
pnpm install

# 使用 yarn
yarn install
```

### 开发环境

```bash
# 启动开发服务器 (http://localhost:3000)
npm run dev

# 使用其他包管理器
pnpm dev
yarn dev
```

### 生产构建

```bash
# 构建生产版本
npm run build

# 预览生产构建
npm run preview

# 生成静态站点
npm run generate
```

## 📁 项目结构

```
winray1/
├── app/                          # Nuxt 4 应用目录
│   ├── assets/css/              # 样式文件
│   │   └── main.css            # Tailwind 4 主样式文件
│   ├── components/             # Vue 组件
│   │   └── InquiryDrawer.vue   # 询问抽屉组件
│   ├── layouts/                # 布局组件
│   │   └── default.vue         # 默认布局
│   ├── pages/                  # 页面组件 (自动路由)
│   │   ├── index.vue           # 首页
│   │   ├── about.vue           # 关于我们
│   │   ├── products/           # 产品页面
│   │   ├── services/           # 服务页面
│   │   └── solutions/          # 解决方案页面
│   └── app.vue                 # 根组件
├── public/                     # 静态资源
│   ├── img/                   # 图片资源
│   └── favicon.png            # 网站图标
├── 赢睿科技官网栏目文案/         # 内容文案资源
│   ├── 产品介绍/              # 产品文案
│   ├── 解决方案/              # 解决方案文案
│   └── 服务体系/              # 服务体系文案
├── .cursor/                   # Cursor IDE 规则
├── nuxt.config.ts            # Nuxt 4 配置文件
├── package.json              # 项目依赖
├── tsconfig.json             # TypeScript 配置
└── README.md                 # 项目文档
```

## 🎨 设计系统

### 品牌色彩

```css
/* 主品牌色 */
--color-primary-500: #008891; /* 科技绿 */
--color-primary-600: #007a82; /* 深科技绿 */

/* 辅助色彩 */
--color-winray-500: #008891; /* 赢睿品牌色 */
--color-accent-500: #f76262; /* 强调色 */
```

### 字体系统

项目使用多语言字体栈，优先中文显示：

```css
font-family: "Roboto", "PingFang SC", "Hiragino Sans GB", "Noto Sans",
  "Microsoft YaHei", sans-serif;
```

### 设计原则

- **无圆角设计**: 所有 UI 元素使用直角设计，体现专业科技感
- **响应式优先**: 移动端优先的设计理念
- **品牌一致性**: 统一的色彩和字体规范

## 🔧 开发指南

### Nuxt 4 最佳实践

#### 1. 组件开发

```vue
<script setup lang="ts">
// 使用 Composition API + TypeScript
interface Props {
  title: string;
  description?: string;
}

const props = withDefaults(defineProps<Props>(), {
  description: "默认描述",
});
</script>

<template>
  <div>
    <h1>{{ title }}</h1>
    <p>{{ description }}</p>
  </div>
</template>
```

#### 2. 页面 SEO 配置

```vue
<script setup lang="ts">
// 使用 useHead 进行 SEO 优化
useHead({
  title: "AI风控定价系统 - 赢睿保险科技",
  meta: [
    { name: "description", content: "基于AI大语言模型的保险风控定价系统" },
    { property: "og:title", content: "AI风控定价系统" },
  ],
});
</script>
```

### Tailwind 4 使用技巧

#### 1. 自定义主题变量

```css
@theme {
  /* 定义自定义颜色变量 */
  --color-brand-primary: #008891;
  --color-brand-secondary: #f76262;
}
```

#### 2. 组件样式组织

```css
@layer components {
  .product-card {
    @apply bg-white shadow-lg hover:shadow-xl
           transition-all duration-300 ease-out;
  }
}
```

### 性能优化

#### 1. 图像优化

```vue
<template>
  <!-- 使用 @nuxt/image 进行自动优化 -->
  <NuxtImg
    src="/img/hero-image.jpg"
    alt="赢睿科技"
    width="1200"
    height="600"
    loading="lazy"
    format="webp"
  />
</template>
```

#### 2. 代码分割

```vue
<script setup lang="ts">
// 异步组件加载
const HeavyComponent = defineAsyncComponent(
  () => import("~/components/HeavyComponent.vue")
);
</script>
```

## 📝 内容管理

项目包含完整的内容文案系统，位于 `赢睿科技官网栏目文案/` 目录：

### 内容结构

- **产品介绍**: AI 智能、IT 基建、核心系统、营销获客
- **解决方案**: 保险公司、保险中介、保险 MGA、物流行业
- **服务体系**: 数智化转型、合规咨询、运营服务

### 内容更新流程

1. 编辑对应的 Markdown 文件
2. 更新页面组件中的内容引用
3. 确保 SEO 信息同步更新

## 🚀 部署指南

### 静态部署

```bash
# 生成静态文件
npm run generate

# 部署 .output/public 目录到静态托管服务
```

### 服务端部署

```bash
# 构建生产版本
npm run build

# 启动生产服务器
npm run preview
```

### 环境变量

```bash
# .env
NUXT_PUBLIC_SITE_URL=https://www.yingrtech.com
NUXT_PUBLIC_GTM_ID=GTM-XXXXXXX
```

## 🔍 常见问题

### Q: 如何从 Nuxt 3 迁移到 Nuxt 4？

A: 主要需要调整目录结构，将组件移动到 `app/` 目录下，并更新配置文件。

### Q: Tailwind 4 配置文件在哪里？

A: Tailwind 4 不再需要单独的配置文件，主题配置直接写在 CSS 文件中的 `@theme` 指令里。

### Q: 如何自定义品牌色彩？

A: 在 `app/assets/css/main.css` 文件的 `@theme` 部分添加自定义颜色变量。

### Q: 如何优化页面加载性能？

A: 使用 `@nuxt/image` 进行图像优化，合理使用懒加载和代码分割。

## 🤝 贡献指南

1. Fork 项目仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目为赢睿保险科技有限公司内部项目，版权所有。

## 📞 联系方式

- **公司网站**: [https://www.yingrtech.com](https://www.yingrtech.com)
- **技术支持**: <EMAIL>
- **项目维护**: Eric Zeng

---

<div align="center">
  <strong>赢睿保险科技 - 用 AI 重新定义保险</strong>
</div>
