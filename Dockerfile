FROM public.ecr.aws/docker/library/node:22-trixie-slim AS builder

RUN sed -i -e 's/deb.debian.org/mirrors.cloud.aliyuncs.com/g' /etc/apt/sources.list.d/debian.sources \
    && apt-get update \
    && apt-get -qq --no-install-recommends install build-essential python3 -y

WORKDIR /app

COPY package*.json ./

RUN npm --registry=https://registry.npmmirror.com  install

COPY . .

RUN npm run build

FROM public.ecr.aws/docker/library/node:22-trixie-slim AS production

WORKDIR /app

COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/.output ./.output
COPY --from=builder /app/package.json ./package.json

ENV NODE_ENV=production

EXPOSE 3000

CMD ["node", ".output/server/index.mjs"]
