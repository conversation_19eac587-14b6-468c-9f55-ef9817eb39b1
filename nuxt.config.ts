// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: "2025-07-15",
  devtools: { enabled: true },
  modules: ["@nuxt/image", "@nuxt/scripts", "@nuxt/ui", "@tailwindcss/vite"],
  css: ["~/assets/css/main.css"],
  ui: {
    fonts: false,
  },
  // 应用配置
  app: {
    head: {
      link: [
        { rel: "icon", type: "image/png", href: "/favicon.png" },
        { rel: "icon", type: "image/x-icon", href: "/favicon.ico" },
        { rel: "apple-touch-icon", href: "/apple-touch-icon.png" },
        { rel: "manifest", href: "/site.webmanifest" },
      ],
      meta: [
        { name: "msapplication-TileColor", content: "#008891" },
        { name: "theme-color", content: "#008891" },
        { name: "apple-mobile-web-app-capable", content: "yes" },
        { name: "apple-mobile-web-app-status-bar-style", content: "default" },
        { name: "apple-mobile-web-app-title", content: "赢睿科技" },
      ],
    },
  },

  // Nitro 配置
  nitro: {
    prerender: {
      // 忽略预渲染错误，允许生成包含404页面的静态站点
      failOnError: false,
      // 只预渲染存在的路由
      routes: ["/"],
    },
  },
});
